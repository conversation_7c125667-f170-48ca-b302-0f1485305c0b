#!/usr/bin/env python3
"""
星火HTTP服务属性错误修复验证脚本
测试SparkHTTPService对象的属性访问是否正常
"""

import sys
import os

def test_spark_http_service_attributes():
    """测试星火HTTP服务属性访问"""
    print("🔧 星火HTTP服务属性修复验证测试")
    print("=" * 50)
    
    try:
        # 添加backend-services目录到Python路径
        backend_services_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend-services')
        if backend_services_path not in sys.path:
            sys.path.append(backend_services_path)
        
        print("=== 测试SparkHTTPService属性 ===")
        
        # 设置测试环境变量
        os.environ['SPARK_API_PASSWORD'] = 'test_password'
        
        from services.spark_http_service import SparkHTTPService
        
        # 创建服务实例
        service = SparkHTTPService()
        
        # 测试所有应该存在的属性
        print(f"✅ api_password: {service.api_password}")
        print(f"✅ model: {service.model}")
        print(f"✅ url: {service.url}")
        
        # 测试之前错误的属性访问
        try:
            chat_url = getattr(service, 'chat_url', None)
            domain = getattr(service, 'domain', None)
            print(f"⚠️ chat_url (不存在): {chat_url}")
            print(f"⚠️ domain (不存在): {domain}")
        except AttributeError as e:
            print(f"❌ 属性错误: {e}")
        
        print("\n=== 测试修复后的日志格式 ===")
        # 模拟修复后的日志输出
        log_message = f"星火HTTP服务配置 - URL: {service.url}, Model: {service.model}"
        print(f"✅ 修复后的日志: {log_message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始星火HTTP服务属性修复验证")
    
    # 测试结果
    success = test_spark_http_service_attributes()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"SparkHTTPService属性测试: {'✅' if success else '❌'}")
    
    if success:
        print("\n🎉 测试通过！星火HTTP服务属性错误已修复！")
        print("\n📝 修复内容:")
        print("- 将 spark_http_service.chat_url 改为 spark_http_service.url")
        print("- 将 spark_http_service.domain 改为 spark_http_service.model")
        print("- 确保日志输出使用正确的属性名称")
    else:
        print("\n❌ 测试失败，请检查修复内容")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
