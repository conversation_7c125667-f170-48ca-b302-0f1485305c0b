#!/usr/bin/env python3
"""
测试新增的通用AI聊天接口 /api/chat/spark
用于验证情感分析功能是否能正常工作
"""

import requests
import json
import time

def test_generic_chat_api():
    """测试通用AI聊天接口"""
    print("🧪 测试通用AI聊天接口 /api/chat/spark")
    
    base_url = "http://localhost:8000"
    
    # 首先需要登录获取认证token
    print("\n1️⃣ 尝试登录获取认证token...")
    
    # 测试用户登录
    login_url = f"{base_url}/api/auth/login/"
    login_data = {
        "username": "api_test_user",
        "password": "testpass123"
    }
    
    try:
        login_response = requests.post(
            login_url,
            json=login_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            print(f"登录响应: {login_result}")
            access_token = login_result.get('access_token') or login_result.get('access') or login_result.get('token')
            if access_token:
                print(f"✅ 登录成功，获取到token: {access_token[:20]}...")
            else:
                print("⚠️ 登录响应中未找到access_token")
                access_token = None
        else:
            print(f"⚠️ 登录失败: {login_response.status_code}")
            print(f"响应内容: {login_response.text}")
            print("尝试使用默认认证...")
            access_token = None
            
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        print("尝试使用默认认证...")
        access_token = None
    
    # 测试通用聊天接口
    print("\n2️⃣ 测试通用AI聊天接口...")
    
    chat_url = f"{base_url}/api/chat/spark/"
    
    # 构建测试消息（模拟情感分析请求）
    test_data = {
        "model": "spark-4.0-ultra",
        "messages": [
            {
                "role": "system",
                "content": "你是一个情感分析专家，请分析用户输入的情感，并返回JSON格式的结果，包含emotion（情感类型）、intensity（强度1-10）、expression（表情）、motion（动作）等字段。"
            },
            {
                "role": "user",
                "content": "我今天很开心，因为完成了一个重要的项目！"
            }
        ],
        "stream": False,
        "max_tokens": 2048,
        "temperature": 0.7
    }
    
    # 设置请求头
    headers = {'Content-Type': 'application/json'}
    if access_token:
        headers['Authorization'] = f'Bearer {access_token}'
    
    try:
        print(f"📤 发送请求到: {chat_url}")
        print(f"📝 请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(
            chat_url,
            json=test_data,
            headers=headers,
            timeout=30
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ 通用AI聊天接口测试成功!")
            print(f"📄 响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            # 检查响应格式是否符合OpenAI标准
            if 'choices' in response_data and len(response_data['choices']) > 0:
                ai_content = response_data['choices'][0]['message']['content']
                print(f"🤖 AI回复内容: {ai_content}")
                return True
            else:
                print("⚠️ 响应格式不符合预期")
                return False
                
        elif response.status_code == 401:
            print("❌ 认证失败，需要有效的用户token")
            return False
        elif response.status_code == 404:
            print("❌ 接口不存在，路由配置可能有问题")
            return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_stream_response():
    """测试流式响应"""
    print("\n3️⃣ 测试流式响应...")

    base_url = "http://localhost:8000"

    # 先登录获取token
    login_url = f"{base_url}/api/auth/login/"
    login_data = {
        "username": "api_test_user",
        "password": "testpass123"
    }

    try:
        login_response = requests.post(
            login_url,
            json=login_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )

        if login_response.status_code == 200:
            login_result = login_response.json()
            access_token = login_result.get('token')
        else:
            print(f"⚠️ 流式测试登录失败: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 流式测试登录异常: {e}")
        return False

    chat_url = f"{base_url}/api/chat/spark/"

    test_data = {
        "model": "spark-4.0-ultra",
        "messages": [
            {
                "role": "user",
                "content": "请简单介绍一下你自己"
            }
        ],
        "stream": True
    }

    headers = {'Content-Type': 'application/json'}
    if access_token:
        headers['Authorization'] = f'Bearer {access_token}'

    try:
        response = requests.post(
            chat_url,
            json=test_data,
            headers=headers,
            stream=True,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 流式响应测试开始...")
            for line in response.iter_lines():
                if line:
                    line_text = line.decode('utf-8')
                    if line_text.startswith('data: '):
                        data_part = line_text[6:]  # 移除 'data: ' 前缀
                        if data_part != '[DONE]':
                            try:
                                chunk_data = json.loads(data_part)
                                print(f"📦 收到数据块: {json.dumps(chunk_data, ensure_ascii=False)}")
                            except json.JSONDecodeError:
                                print(f"📦 收到原始数据: {data_part}")
            print("✅ 流式响应测试完成")
            return True
        else:
            print(f"❌ 流式响应测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 流式响应测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试通用AI聊天接口")
    print("=" * 50)
    
    # 测试基本功能
    basic_test_result = test_generic_chat_api()
    
    # 测试流式响应
    stream_test_result = test_stream_response()
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"   基本聊天功能: {'✅ 通过' if basic_test_result else '❌ 失败'}")
    print(f"   流式响应功能: {'✅ 通过' if stream_test_result else '❌ 失败'}")
    
    if basic_test_result and stream_test_result:
        print("\n🎉 所有测试通过！通用AI聊天接口工作正常")
    else:
        print("\n⚠️ 部分测试失败，请检查配置和服务状态")
