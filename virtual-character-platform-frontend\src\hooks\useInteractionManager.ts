import { useState, useCallback, useRef, useEffect } from 'react';
import { message } from 'antd';

// requestIdleCallback polyfill for better browser compatibility
const requestIdleCallbackPolyfill = (callback: () => void) => {
  if (typeof requestIdleCallback !== 'undefined') {
    requestIdleCallback(callback);
  } else {
    // Fallback for browsers that don't support requestIdleCallback
    setTimeout(callback, 0);
  }
};

/**
 * 交互模式枚举
 */
export type InteractionMode = 'idle' | 'voice' | 'touch';

/**
 * 交互状态接口
 */
export interface InteractionState {
  mode: InteractionMode;
  isVoiceActive: boolean;
  isTouchActive: boolean;
  isAISpeaking: boolean;
  priority: 'voice' | 'touch' | null;
  lastInteractionTime: number;
  interactionCount: number;
}

/**
 * 交互管理器配置
 */
export interface InteractionManagerConfig {
  voicePriority: boolean; // 语音是否优先
  touchTimeout: number; // 触摸交互超时时间(ms)
  voiceTimeout: number; // 语音交互超时时间(ms)
  enableLogging: boolean; // 是否启用日志
  maxConcurrentInteractions: number; // 最大并发交互数
}

/**
 * 交互事件回调
 */
export interface InteractionCallbacks {
  onVoiceStart?: () => void;
  onVoiceEnd?: () => void;
  onTouchStart?: () => void;
  onTouchEnd?: () => void;
  onModeChange?: (mode: InteractionMode) => void;
  onConflict?: (currentMode: InteractionMode, requestedMode: InteractionMode) => void;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: InteractionManagerConfig = {
  voicePriority: true,
  touchTimeout: 5000,
  voiceTimeout: 30000,
  enableLogging: true,
  maxConcurrentInteractions: 1,
};

/**
 * 全局交互状态管理Hook
 * 
 * 功能：
 * - 管理语音和触摸交互的互斥机制
 * - 实现语音优先策略
 * - 提供交互状态监控和日志
 * - 支持超时自动重置
 * - 防止交互冲突
 */
export const useInteractionManager = (
  config: Partial<InteractionManagerConfig> = {},
  callbacks: InteractionCallbacks = {}
) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const interactionStartTime = useRef<number>(0);

  // 交互状态
  const [state, setState] = useState<InteractionState>({
    mode: 'idle',
    isVoiceActive: false,
    isTouchActive: false,
    isAISpeaking: false,
    priority: null,
    lastInteractionTime: 0,
    interactionCount: 0,
  });

  /**
   * 记录日志
   */
  const log = useCallback((message: string, level: 'info' | 'warn' | 'error' = 'info') => {
    if (!finalConfig.enableLogging) return;
    
    const timestamp = new Date().toLocaleTimeString();
    const prefix = `[InteractionManager ${timestamp}]`;
    
    switch (level) {
      case 'warn':
        console.warn(prefix, message);
        break;
      case 'error':
        console.error(prefix, message);
        break;
      default:
        console.log(prefix, message);
    }
  }, [finalConfig.enableLogging]);

  /**
   * 清除超时定时器
   */
  const clearInteractionTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  /**
   * 设置交互超时
   */
  const setInteractionTimeout = useCallback((mode: InteractionMode, duration: number) => {
    clearInteractionTimeout();

    timeoutRef.current = setTimeout(() => {
      log(`${mode} 交互超时检查开始`);

      // 使用异步方式处理超时，避免阻塞主线程
      requestIdleCallbackPolyfill(() => {
        setState(currentState => {
          // 检查是否AI正在说话，如果是则延长超时
          if (currentState.isAISpeaking) {
            log(`${mode} 交互超时，但AI正在说话，延长超时时间`, 'info');
            // 避免递归调用，使用新的setTimeout
            setTimeout(() => {
              setInteractionTimeout(mode, 30000);
            }, 100);
            return currentState;
          }

          log(`${mode} 交互超时，准备重置为idle模式`, 'warn');
          // 异步执行重置，避免状态更新冲突
          setTimeout(() => {
            resetToIdle();
          }, 50);
          return currentState;
        });
      });
    }, duration);
  }, [clearInteractionTimeout, log]);

  /**
   * 重置到空闲状态
   */
  const resetToIdle = useCallback(() => {
    clearInteractionTimeout();

    setState(currentState => {
      const previousMode = currentState.mode;

      // 如果已经是idle状态，不需要重复重置
      if (previousMode === 'idle') {
        log('已经是idle状态，跳过重置');
        return currentState;
      }

      // 如果AI正在说话，延迟重置
      if (currentState.isAISpeaking) {
        log('AI正在说话，延迟重置到idle状态');
        // 使用requestIdleCallback避免阻塞主线程
        requestIdleCallbackPolyfill(() => {
          setTimeout(() => {
            setState(latestState => {
              if (!latestState.isAISpeaking && latestState.mode !== 'idle') {
                log('延迟重置执行：AI已停止说话，重置到idle状态');
                callbacks.onModeChange?.('idle');
                return {
                  ...latestState,
                  mode: 'idle',
                  isVoiceActive: false,
                  isTouchActive: false,
                  priority: null,
                };
              }
              return latestState;
            });
          }, 5000);
        });
        return currentState;
      }

      log(`交互状态重置为idle (从 ${previousMode})`);
      // 异步执行回调，避免阻塞主线程
      requestIdleCallbackPolyfill(() => {
        callbacks.onModeChange?.('idle');
      });

      return {
        ...currentState,
        mode: 'idle',
        isVoiceActive: false,
        isTouchActive: false,
        priority: null,
      };
    });
  }, [clearInteractionTimeout, log, callbacks]);

  /**
   * 开始语音交互
   */
  const startVoiceInteraction = useCallback(() => {
    log('尝试开始语音交互');

    // 检查当前状态
    if (state.isVoiceActive) {
      log('语音交互已经激活，忽略重复请求', 'warn');
      return { success: true, reason: 'already_active' };
    }

    // 如果触摸交互正在进行
    if (state.isTouchActive) {
      if (finalConfig.voicePriority) {
        log('语音优先，中断触摸交互');
        callbacks.onTouchEnd?.();
      } else {
        log('触摸交互进行中，语音请求被拒绝', 'warn');
        callbacks.onConflict?.('touch', 'voice');
        message.warning('触摸交互进行中，请稍后再试');
        return { success: false, reason: 'touch_active' };
      }
    }

    // 开始语音交互
    interactionStartTime.current = Date.now();
    
    setState(prev => ({
      ...prev,
      mode: 'voice',
      isVoiceActive: true,
      isTouchActive: false,
      priority: 'voice',
      lastInteractionTime: Date.now(),
      interactionCount: prev.interactionCount + 1,
    }));

    // 设置超时
    setInteractionTimeout('voice', finalConfig.voiceTimeout);

    log('语音交互已开始');
    callbacks.onVoiceStart?.();
    callbacks.onModeChange?.('voice');

    return { success: true, reason: 'started' };
  }, [state, finalConfig, callbacks, log, setInteractionTimeout]);

  /**
   * 结束语音交互
   */
  const endVoiceInteraction = useCallback(() => {
    if (!state.isVoiceActive) {
      log('语音交互未激活，忽略结束请求', 'warn');
      return;
    }

    const duration = Date.now() - interactionStartTime.current;
    log(`语音交互结束，持续时间: ${duration}ms`);

    clearInteractionTimeout();
    
    setState(prev => ({
      ...prev,
      mode: 'idle',
      isVoiceActive: false,
      priority: null,
    }));

    callbacks.onVoiceEnd?.();
    callbacks.onModeChange?.('idle');
  }, [state.isVoiceActive, clearInteractionTimeout, log, callbacks]);

  /**
   * 开始触摸交互
   */
  const startTouchInteraction = useCallback(() => {
    log('尝试开始触摸交互');

    // 检查当前状态
    if (state.isTouchActive) {
      log('触摸交互已经激活，忽略重复请求', 'warn');
      return { success: true, reason: 'already_active' };
    }

    // 如果语音交互正在进行
    if (state.isVoiceActive) {
      log('语音交互进行中，触摸请求被拒绝', 'warn');
      callbacks.onConflict?.('voice', 'touch');
      message.warning('语音交互进行中，请稍后再试');
      return { success: false, reason: 'voice_active' };
    }

    // 如果AI正在说话
    if (state.isAISpeaking) {
      log('AI正在说话，触摸请求被拒绝', 'warn');
      callbacks.onConflict?.('voice', 'touch');
      message.warning('AI正在说话，请稍后再试');
      return { success: false, reason: 'ai_speaking' };
    }

    // 开始触摸交互
    interactionStartTime.current = Date.now();
    
    setState(prev => ({
      ...prev,
      mode: 'touch',
      isTouchActive: true,
      priority: 'touch',
      lastInteractionTime: Date.now(),
      interactionCount: prev.interactionCount + 1,
    }));

    // 设置超时
    setInteractionTimeout('touch', finalConfig.touchTimeout);

    log('触摸交互已开始');
    callbacks.onTouchStart?.();
    callbacks.onModeChange?.('touch');

    return { success: true, reason: 'started' };
  }, [state, finalConfig, callbacks, log, setInteractionTimeout]);

  /**
   * 结束触摸交互
   */
  const endTouchInteraction = useCallback(() => {
    if (!state.isTouchActive) {
      log('触摸交互未激活，忽略结束请求', 'warn');
      return;
    }

    const duration = Date.now() - interactionStartTime.current;
    log(`触摸交互结束，持续时间: ${duration}ms`);

    clearInteractionTimeout();
    
    setState(prev => ({
      ...prev,
      mode: 'idle',
      isTouchActive: false,
      priority: null,
    }));

    callbacks.onTouchEnd?.();
    callbacks.onModeChange?.('idle');
  }, [state.isTouchActive, clearInteractionTimeout, log, callbacks]);

  /**
   * 设置AI说话状态
   */
  const setAISpeaking = useCallback((speaking: boolean) => {
    log(`AI说话状态变更: ${speaking ? '开始' : '结束'}`);

    setState(prev => {
      // 如果状态没有变化，不需要更新
      if (prev.isAISpeaking === speaking) {
        return prev;
      }

      return {
        ...prev,
        isAISpeaking: speaking,
      };
    });

    // 如果AI开始说话，清除可能存在的交互超时
    if (speaking) {
      clearInteractionTimeout();
    }
  }, [log, clearInteractionTimeout]);

  /**
   * 检查是否可以进行交互
   */
  const canInteract = useCallback((mode: InteractionMode): boolean => {
    switch (mode) {
      case 'voice':
        return !state.isTouchActive && !state.isAISpeaking;
      case 'touch':
        return !state.isVoiceActive && !state.isAISpeaking;
      default:
        return true;
    }
  }, [state]);

  /**
   * 获取交互统计信息
   */
  const getStats = useCallback(() => {
    const now = Date.now();
    const uptime = state.lastInteractionTime ? now - state.lastInteractionTime : 0;

    return {
      totalInteractions: state.interactionCount,
      currentMode: state.mode,
      isActive: state.mode !== 'idle',
      lastInteractionTime: state.lastInteractionTime,
      uptime,
      isHealthy: state.interactionCount > 0 ? uptime < finalConfig.voiceTimeout * 2 : true,
      config: finalConfig,
    };
  }, [state, finalConfig]);

  /**
   * 获取详细的调试信息
   */
  const getDebugInfo = useCallback(() => {
    return {
      state,
      config: finalConfig,
      hasTimeout: timeoutRef.current !== null,
      interactionDuration: interactionStartTime.current ? Date.now() - interactionStartTime.current : 0,
      callbacks: {
        hasVoiceStart: !!callbacks.onVoiceStart,
        hasVoiceEnd: !!callbacks.onVoiceEnd,
        hasTouchStart: !!callbacks.onTouchStart,
        hasTouchEnd: !!callbacks.onTouchEnd,
        hasModeChange: !!callbacks.onModeChange,
        hasConflict: !!callbacks.onConflict,
      },
    };
  }, [state, finalConfig, callbacks]);

  /**
   * 强制重置（用于调试和错误恢复）
   */
  const forceReset = useCallback(() => {
    log('执行强制重置', 'warn');
    clearInteractionTimeout();

    setState({
      mode: 'idle',
      isVoiceActive: false,
      isTouchActive: false,
      isAISpeaking: false,
      priority: null,
      lastInteractionTime: 0,
      interactionCount: 0,
    });

    callbacks.onModeChange?.('idle');
  }, [clearInteractionTimeout, log, callbacks]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      clearInteractionTimeout();
    };
  }, [clearInteractionTimeout]);

  // 开发模式下的调试日志
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && finalConfig.enableLogging) {
      log(`交互状态变更: ${JSON.stringify(state)}`, 'info');
    }
  }, [state, finalConfig.enableLogging, log]);

  return {
    // 状态
    state,

    // 控制方法
    startVoiceInteraction,
    endVoiceInteraction,
    startTouchInteraction,
    endTouchInteraction,
    setAISpeaking,
    resetToIdle,
    forceReset,

    // 查询方法
    canInteract,
    getStats,
    getDebugInfo,

    // 便捷属性
    isIdle: state.mode === 'idle',
    isVoiceActive: state.isVoiceActive,
    isTouchActive: state.isTouchActive,
    isAISpeaking: state.isAISpeaking,
    currentMode: state.mode,
  };
};
