/**
 * 语音功能配置文件
 * 
 * 此文件包含了语音交互、口型同步、交互管理等功能的所有配置参数
 * 可以根据需要调整这些参数来优化用户体验和性能
 */

// 口型同步配置
export interface LipSyncConfig {
  // 音频分析参数
  fftSize: number;                    // FFT大小，影响频率分辨率
  smoothingTimeConstant: number;      // 音频分析平滑常数
  volumeThreshold: number;            // 音量阈值，低于此值认为无声
  
  // 口型识别参数
  smoothingFactor: number;            // 口型变化平滑因子 (0-1)
  visemeHistorySize: number;          // 口型历史记录大小
  frequencyRanges: {                 // 频率范围定义
    low: [number, number];           // 低频范围 (aa, oh)
    mid: [number, number];           // 中频范围 (eh, ih)  
    high: [number, number];          // 高频范围 (ee, s)
  };
  
  // 性能和调试
  enableLogging: boolean;             // 是否启用日志
  performanceMonitoring: boolean;     // 是否启用性能监控
  maxErrorCount: number;              // 最大错误数，超过后禁用日志
  performanceWarningThreshold: number; // 性能警告阈值(ms)
}

// 交互管理配置
export interface InteractionConfig {
  // 优先级和超时
  voicePriority: boolean;             // 语音是否优先于触摸
  touchTimeout: number;               // 触摸交互超时时间(ms)
  voiceTimeout: number;               // 语音交互超时时间(ms)
  aiSpeakingTimeout: number;          // AI说话超时时间(ms)
  
  // 冲突处理
  maxConcurrentInteractions: number;  // 最大并发交互数
  conflictResolutionDelay: number;    // 冲突解决延迟(ms)
  
  // 用户体验
  showWarningMessages: boolean;       // 是否显示警告消息
  warningMessageDuration: number;     // 警告消息显示时长(ms)
  enableHapticFeedback: boolean;      // 是否启用触觉反馈
  
  // 调试和监控
  enableLogging: boolean;             // 是否启用日志
  enableDebugMode: boolean;           // 是否启用调试模式
  logLevel: 'info' | 'warn' | 'error'; // 日志级别
}

// 音频处理配置
export interface AudioConfig {
  // 音频上下文
  sampleRate: number;                 // 采样率
  bufferSize: number;                 // 缓冲区大小
  
  // 音量处理
  volumeMultiplier: number;           // 音量放大倍数
  volumeSigmoidFactor: number;        // Sigmoid函数因子
  volumeSigmoidOffset: number;        // Sigmoid函数偏移
  
  // 质量和性能
  audioQuality: 'low' | 'medium' | 'high'; // 音频质量
  enableAudioCompression: boolean;    // 是否启用音频压缩
  maxAudioDuration: number;           // 最大音频时长(秒)
}

// 默认配置
export const DEFAULT_LIPSYNC_CONFIG: LipSyncConfig = {
  // 音频分析参数
  fftSize: 2048,
  smoothingTimeConstant: 0.8,
  volumeThreshold: 0.1,
  
  // 口型识别参数
  smoothingFactor: 0.3,
  visemeHistorySize: 5,
  frequencyRanges: {
    low: [0, 300],
    mid: [300, 2000],
    high: [2000, 8000],
  },
  
  // 性能和调试
  enableLogging: true,
  performanceMonitoring: true,
  maxErrorCount: 10,
  performanceWarningThreshold: 5.0,
};

export const DEFAULT_INTERACTION_CONFIG: InteractionConfig = {
  // 优先级和超时
  voicePriority: true,
  touchTimeout: 5000,
  voiceTimeout: 30000,
  aiSpeakingTimeout: 10000,
  
  // 冲突处理
  maxConcurrentInteractions: 1,
  conflictResolutionDelay: 100,
  
  // 用户体验
  showWarningMessages: true,
  warningMessageDuration: 3000,
  enableHapticFeedback: false,
  
  // 调试和监控
  enableLogging: true,
  enableDebugMode: false,
  logLevel: 'info',
};

export const DEFAULT_AUDIO_CONFIG: AudioConfig = {
  // 音频上下文
  sampleRate: 44100,
  bufferSize: 4096,
  
  // 音量处理
  volumeMultiplier: 1.2,
  volumeSigmoidFactor: 45,
  volumeSigmoidOffset: 5,
  
  // 质量和性能
  audioQuality: 'medium',
  enableAudioCompression: false,
  maxAudioDuration: 60,
};

// 配置管理类
export class VoiceConfigManager {
  private static instance: VoiceConfigManager;
  private lipSyncConfig: LipSyncConfig;
  private interactionConfig: InteractionConfig;
  private audioConfig: AudioConfig;

  private constructor() {
    // 从localStorage加载配置，如果没有则使用默认配置
    this.lipSyncConfig = this.loadConfig('lipSyncConfig', DEFAULT_LIPSYNC_CONFIG);
    this.interactionConfig = this.loadConfig('interactionConfig', DEFAULT_INTERACTION_CONFIG);
    this.audioConfig = this.loadConfig('audioConfig', DEFAULT_AUDIO_CONFIG);
  }

  public static getInstance(): VoiceConfigManager {
    if (!VoiceConfigManager.instance) {
      VoiceConfigManager.instance = new VoiceConfigManager();
    }
    return VoiceConfigManager.instance;
  }

  private loadConfig<T>(key: string, defaultConfig: T): T {
    try {
      const stored = localStorage.getItem(`voiceConfig_${key}`);
      if (stored) {
        return { ...defaultConfig, ...JSON.parse(stored) };
      }
    } catch (error) {
      console.warn(`Failed to load config ${key}:`, error);
    }
    return defaultConfig;
  }

  private saveConfig<T>(key: string, config: T): void {
    try {
      localStorage.setItem(`voiceConfig_${key}`, JSON.stringify(config));
    } catch (error) {
      console.warn(`Failed to save config ${key}:`, error);
    }
  }

  // Getter方法
  public getLipSyncConfig(): LipSyncConfig {
    return { ...this.lipSyncConfig };
  }

  public getInteractionConfig(): InteractionConfig {
    return { ...this.interactionConfig };
  }

  public getAudioConfig(): AudioConfig {
    return { ...this.audioConfig };
  }

  // Setter方法
  public updateLipSyncConfig(updates: Partial<LipSyncConfig>): void {
    this.lipSyncConfig = { ...this.lipSyncConfig, ...updates };
    this.saveConfig('lipSyncConfig', this.lipSyncConfig);
  }

  public updateInteractionConfig(updates: Partial<InteractionConfig>): void {
    this.interactionConfig = { ...this.interactionConfig, ...updates };
    this.saveConfig('interactionConfig', this.interactionConfig);
  }

  public updateAudioConfig(updates: Partial<AudioConfig>): void {
    this.audioConfig = { ...this.audioConfig, ...updates };
    this.saveConfig('audioConfig', this.audioConfig);
  }

  // 重置配置
  public resetToDefaults(): void {
    this.lipSyncConfig = { ...DEFAULT_LIPSYNC_CONFIG };
    this.interactionConfig = { ...DEFAULT_INTERACTION_CONFIG };
    this.audioConfig = { ...DEFAULT_AUDIO_CONFIG };
    
    this.saveConfig('lipSyncConfig', this.lipSyncConfig);
    this.saveConfig('interactionConfig', this.interactionConfig);
    this.saveConfig('audioConfig', this.audioConfig);
  }

  // 导出配置
  public exportConfig(): string {
    return JSON.stringify({
      lipSync: this.lipSyncConfig,
      interaction: this.interactionConfig,
      audio: this.audioConfig,
    }, null, 2);
  }

  // 导入配置
  public importConfig(configJson: string): boolean {
    try {
      const config = JSON.parse(configJson);
      
      if (config.lipSync) {
        this.updateLipSyncConfig(config.lipSync);
      }
      if (config.interaction) {
        this.updateInteractionConfig(config.interaction);
      }
      if (config.audio) {
        this.updateAudioConfig(config.audio);
      }
      
      return true;
    } catch (error) {
      console.error('Failed to import config:', error);
      return false;
    }
  }

  // 验证配置
  public validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证口型同步配置
    if (this.lipSyncConfig.smoothingFactor < 0 || this.lipSyncConfig.smoothingFactor > 1) {
      errors.push('口型平滑因子必须在0-1之间');
    }
    if (this.lipSyncConfig.fftSize < 256 || this.lipSyncConfig.fftSize > 32768) {
      errors.push('FFT大小必须在256-32768之间');
    }

    // 验证交互配置
    if (this.interactionConfig.touchTimeout < 1000) {
      errors.push('触摸超时时间不能少于1秒');
    }
    if (this.interactionConfig.voiceTimeout < 5000) {
      errors.push('语音超时时间不能少于5秒');
    }

    // 验证音频配置
    if (this.audioConfig.volumeMultiplier < 0.1 || this.audioConfig.volumeMultiplier > 5.0) {
      errors.push('音量倍数必须在0.1-5.0之间');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// 导出单例实例
export const voiceConfigManager = VoiceConfigManager.getInstance();
