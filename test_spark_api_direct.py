#!/usr/bin/env python3
"""
直接测试星火AI HTTP API
基于您提供的正确示例 http_demo.py
"""

import requests
import json
import os

def test_spark_api_direct():
    """直接测试星火AI HTTP API"""
    print("🌟 直接测试星火AI HTTP API...")
    
    # 检查环境变量
    api_password = os.getenv('SPARK_API_PASSWORD')
    if not api_password:
        print("❌ 环境变量 SPARK_API_PASSWORD 未配置")
        print("💡 请配置环境变量或直接在代码中设置")
        print("📍 获取地址: https://console.xfyun.cn/services/bmx1")
        
        # 如果没有环境变量，提示用户手动输入
        api_password = input("请输入您的SPARK_API_PASSWORD: ").strip()
        if not api_password:
            return False
    
    # 基于您提供的正确示例
    url = "https://spark-api-open.xf-yun.com/v1/chat/completions"
    headers = {
        'Authorization': f'Bearer {api_password}',
        'Content-Type': 'application/json'
    }
    
    # 测试请求体（基于http_demo.py）
    body = {
        "model": "4.0Ultra",
        "user": "test_user",
        "messages": [
            {
                "role": "system",
                "content": "你是神里绫华，一个温柔优雅的角色。"
            },
            {
                "role": "user",
                "content": "你好，绫华！今天天气很好呢。"
            }
        ],
        "stream": False,  # 不使用流式响应，获取完整文字
        "max_tokens": 1024,
        "temperature": 0.7
    }
    
    try:
        print(f"📡 发送请求到: {url}")
        print(f"🔑 使用API密码: {api_password[:10]}...")
        print(f"📝 请求体: {json.dumps(body, ensure_ascii=False, indent=2)}")
        
        response = requests.post(
            url=url,
            json=body,
            headers=headers,
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功！")
            print(f"📝 完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            # 解析响应
            if 'choices' in result and len(result['choices']) > 0:
                choice = result['choices'][0]
                if 'message' in choice and 'content' in choice['message']:
                    ai_response = choice['message']['content']
                    print(f"\n🤖 AI响应内容:")
                    print(f"{'='*50}")
                    print(ai_response)
                    print(f"{'='*50}")
                    
                    # 检查响应质量
                    if len(ai_response) > 10 and "绫华" in ai_response:
                        print("✅ 响应质量优秀：包含角色特征")
                    elif len(ai_response) > 10:
                        print("✅ 响应质量良好")
                    else:
                        print("⚠️ 响应质量一般")
                    
                    return True
                else:
                    print("❌ 响应格式错误：缺少message.content")
                    return False
            else:
                print("❌ 响应格式错误：缺少choices")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"📄 错误信息: {response.text}")
            
            # 分析常见错误
            if response.status_code == 401:
                print("💡 可能的原因：API密码错误或已过期")
            elif response.status_code == 403:
                print("💡 可能的原因：权限不足或账户余额不足")
            elif response.status_code == 429:
                print("💡 可能的原因：请求频率过高")
            
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误，请检查网络")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_streaming_response():
    """测试流式响应（基于http_demo.py的流式版本）"""
    print("\n🌊 测试流式响应...")
    
    api_password = os.getenv('SPARK_API_PASSWORD')
    if not api_password:
        print("❌ 环境变量 SPARK_API_PASSWORD 未配置")
        return False
    
    url = "https://spark-api-open.xf-yun.com/v1/chat/completions"
    headers = {
        'Authorization': f'Bearer {api_password}',
        'Content-Type': 'application/json'
    }
    
    body = {
        "model": "4.0Ultra",
        "user": "test_user",
        "messages": [
            {
                "role": "user",
                "content": "请用一句话介绍一下星火AI。"
            }
        ],
        "stream": True,  # 启用流式响应
        "max_tokens": 512
    }
    
    try:
        print("📡 发送流式请求...")
        response = requests.post(
            url=url,
            json=body,
            headers=headers,
            stream=True,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 流式响应开始:")
            print("🤖 AI回复: ", end="", flush=True)
            
            full_response = ""
            for line in response.iter_lines():
                if line and b'[DONE]' not in line:
                    try:
                        # 解析流式数据（基于http_demo.py）
                        if line.startswith(b'data: '):
                            data_str = line[6:].decode('utf-8')
                            chunk = json.loads(data_str)
                            
                            if 'choices' in chunk and len(chunk['choices']) > 0:
                                delta = chunk['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    content = delta['content']
                                    print(content, end="", flush=True)
                                    full_response += content
                    except json.JSONDecodeError:
                        continue
            
            print(f"\n\n✅ 流式响应完成，总长度: {len(full_response)}")
            return True
        else:
            print(f"❌ 流式请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 流式请求异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 星火AI HTTP API 直接测试")
    print("基于正确的官方示例 http_demo.py")
    print("="*60)
    
    # 显示配置信息
    print("\n📋 配置信息:")
    print("- API端点: https://spark-api-open.xf-yun.com/v1/chat/completions")
    print("- 模型: 4.0Ultra")
    print("- 认证方式: Bearer {APIPassword}")
    print("- 获取APIPassword: https://console.xfyun.cn/services/bmx1")
    
    # 执行测试
    tests = [
        ("非流式响应测试", test_spark_api_direct),
        ("流式响应测试", test_streaming_response),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 执行: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "="*60)
    print("📊 测试结果")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed > 0:
        print("\n🎉 星火AI HTTP API 测试成功！")
        print("💡 现在可以在项目中使用正确的HTTP实现了")
    else:
        print("\n❌ 测试失败，请检查API密码配置")
    
    return passed > 0

if __name__ == "__main__":
    main()
