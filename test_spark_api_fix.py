#!/usr/bin/env python3
"""
测试星火AI API修复
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'virtual_character_platform.settings')
django.setup()

def test_spark_http_service():
    """测试星火HTTP服务"""
    print("🧪 测试星火HTTP服务...")
    
    try:
        # 添加backend-services目录到Python路径
        backend_services_path = os.path.join(os.path.dirname(__file__), 'backend-services')
        if backend_services_path not in sys.path:
            sys.path.append(backend_services_path)
        
        # 导入星火HTTP服务
        from services.spark_http_service import spark_http_service
        
        if spark_http_service is None:
            print("❌ 星火HTTP服务未初始化")
            return False
        
        print("✅ 星火HTTP服务初始化成功")
        
        # 测试连接
        print("🔗 测试星火AI连接...")
        connection_ok = spark_http_service.test_connection()
        
        if connection_ok:
            print("✅ 星火AI连接测试成功")
        else:
            print("❌ 星火AI连接测试失败")
            return False
        
        # 测试对话
        print("💬 测试星火AI对话...")
        response = spark_http_service.get_dialogue_response(
            character_prompt="你是神里绫华，一个优雅的角色。",
            user_message="你好，请介绍一下你自己。"
        )
        
        if response:
            print(f"✅ 星火AI对话测试成功: {response[:100]}...")
            return True
        else:
            print("❌ 星火AI对话测试失败")
            return False
            
    except ImportError as e:
        print(f"❌ 导入星火服务失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 星火服务测试失败: {e}")
        return False

def test_generic_chat_api():
    """测试通用聊天API"""
    print("\n🧪 测试通用聊天API...")
    
    try:
        from django.test import Client
        from django.contrib.auth.models import User
        from rest_framework_simplejwt.tokens import RefreshToken
        
        # 创建测试客户端
        client = Client()
        
        # 创建测试用户
        user, created = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        # 生成JWT token
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        
        # 测试数据
        test_data = {
            'messages': [
                {'role': 'system', 'content': '你是神里绫华，一个优雅的角色。'},
                {'role': 'user', 'content': '你好，请介绍一下你自己。'}
            ],
            'model': '4.0Ultra',
            'stream': True
        }
        
        # 发送请求
        response = client.post(
            '/api/chat/spark/',
            data=test_data,
            content_type='application/json',
            HTTP_AUTHORIZATION=f'Bearer {access_token}'
        )
        
        print(f"📊 API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 通用聊天API测试成功")
            return True
        else:
            print(f"❌ 通用聊天API测试失败: {response.content.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ 通用聊天API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始星火AI API修复测试\n")
    
    # 检查环境变量
    spark_password = os.getenv('SPARK_API_PASSWORD')
    if not spark_password:
        print("❌ SPARK_API_PASSWORD环境变量未设置")
        return
    
    print(f"✅ SPARK_API_PASSWORD已配置: {spark_password[:10]}...")
    
    # 测试星火HTTP服务
    service_ok = test_spark_http_service()
    
    # 测试通用聊天API
    api_ok = test_generic_chat_api()
    
    # 总结
    print(f"\n📋 测试结果总结:")
    print(f"   星火HTTP服务: {'✅ 通过' if service_ok else '❌ 失败'}")
    print(f"   通用聊天API: {'✅ 通过' if api_ok else '❌ 失败'}")
    
    if service_ok and api_ok:
        print("\n🎉 所有测试通过！星火AI API修复成功！")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试。")

if __name__ == '__main__':
    main()
