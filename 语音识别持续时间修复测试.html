<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音识别持续时间修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .test-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .extend-button {
            background: #52c41a;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 12px;
        }
        .extend-button:hover {
            background: #73d13d;
        }
        .log-area {
            background: #f6f6f6;
            border: 1px solid #d9d9d9;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status-display {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status-listening {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .status-idle {
            background: #f6f6f6;
            border: 1px solid #d9d9d9;
            color: #666;
        }
        .transcript-display {
            background: #fff2e8;
            border: 1px solid #ffbb96;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            min-height: 50px;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .warning { color: #faad14; }
        .info { color: #1890ff; }
    </style>
</head>
<body>
    <h1>🎤 语音识别持续时间修复测试</h1>
    
    <div class="test-section">
        <h2>📋 问题描述</h2>
        <p><strong>原问题</strong>: 语音识别持续时间太短，用户还没说完就停止识别</p>
        <p><strong>修复方案</strong>: 
            <br>1. 启用自动重启机制 - 检测到停止后自动重新开始
            <br>2. 增加手动延长功能 - 用户可以主动延长识别时间
            <br>3. 提高最大重启次数 - 支持更长时间的连续识别
        </p>
    </div>

    <div class="test-section">
        <h2>🧪 语音识别测试</h2>
        
        <div class="status-display" id="status-display">
            状态: 待机中
        </div>
        
        <div>
            <button class="test-button" id="start-btn" onclick="startRecognition()">开始语音识别</button>
            <button class="test-button" id="stop-btn" onclick="stopRecognition()" disabled>停止识别</button>
            <button class="extend-button" id="extend-btn" onclick="extendRecognition()" disabled>延长识别时间</button>
        </div>
        
        <div class="transcript-display" id="transcript-display">
            识别结果将显示在这里...
        </div>
        
        <div>
            <strong>重启次数:</strong> <span id="restart-count">0</span>
            <strong>识别时长:</strong> <span id="duration">0</span>秒
        </div>
    </div>

    <div class="test-section">
        <h2>📊 测试日志</h2>
        <button class="test-button" onclick="clearLog()">清除日志</button>
        <div id="log-area" class="log-area"></div>
    </div>

    <div class="test-section">
        <h2>💡 测试建议</h2>
        <ul>
            <li><strong>长句测试</strong>: 说一段较长的话，观察是否会自动重启</li>
            <li><strong>停顿测试</strong>: 说话时故意停顿，看是否能继续识别</li>
            <li><strong>手动延长</strong>: 当识别即将停止时，点击"延长识别时间"按钮</li>
            <li><strong>连续对话</strong>: 模拟真实对话场景，连续说多句话</li>
        </ul>
    </div>

    <script>
        let recognition = null;
        let isListening = false;
        let restartCount = 0;
        let startTime = null;
        let durationTimer = null;
        let shouldRestart = false;
        let maxRestarts = 20;

        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logArea.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log-area').innerHTML = '';
        }

        function updateStatus(status, isActive = false) {
            const statusDisplay = document.getElementById('status-display');
            statusDisplay.textContent = `状态: ${status}`;
            statusDisplay.className = `status-display ${isActive ? 'status-listening' : 'status-idle'}`;
        }

        function updateDuration() {
            if (startTime) {
                const duration = Math.floor((Date.now() - startTime) / 1000);
                document.getElementById('duration').textContent = duration;
            }
        }

        function updateRestartCount() {
            document.getElementById('restart-count').textContent = restartCount;
        }

        function initRecognition() {
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                log('浏览器不支持语音识别', 'error');
                return false;
            }

            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            recognition = new SpeechRecognition();

            // 配置参数
            recognition.continuous = true;      // 连续识别
            recognition.interimResults = true;  // 显示中间结果
            recognition.lang = 'zh-CN';
            recognition.maxAlternatives = 1;

            // 事件监听
            recognition.onstart = function() {
                log('语音识别开始', 'success');
                isListening = true;
                updateStatus('正在监听...', true);
                
                document.getElementById('start-btn').disabled = true;
                document.getElementById('stop-btn').disabled = false;
                document.getElementById('extend-btn').disabled = false;
                
                if (!startTime) {
                    startTime = Date.now();
                    durationTimer = setInterval(updateDuration, 1000);
                }
            };

            recognition.onresult = function(event) {
                let interimTranscript = '';
                let finalTranscript = '';

                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    if (event.results[i].isFinal) {
                        finalTranscript += transcript;
                        log(`最终结果: ${transcript}`, 'success');
                    } else {
                        interimTranscript += transcript;
                    }
                }

                // 更新显示
                const display = document.getElementById('transcript-display');
                display.innerHTML = `
                    <div><strong>最终结果:</strong> ${finalTranscript}</div>
                    <div><strong>中间结果:</strong> <em>${interimTranscript}</em></div>
                `;
            };

            recognition.onend = function() {
                log('语音识别结束', 'warning');
                isListening = false;
                
                // 自动重启逻辑
                if (shouldRestart && restartCount < maxRestarts) {
                    restartCount++;
                    updateRestartCount();
                    log(`自动重启 (${restartCount}/${maxRestarts})`, 'info');
                    
                    setTimeout(() => {
                        if (shouldRestart) {
                            recognition.start();
                        }
                    }, 100);
                } else {
                    // 完全停止
                    updateStatus('识别完成', false);
                    document.getElementById('start-btn').disabled = false;
                    document.getElementById('stop-btn').disabled = true;
                    document.getElementById('extend-btn').disabled = true;
                    
                    if (durationTimer) {
                        clearInterval(durationTimer);
                        durationTimer = null;
                    }
                    
                    if (restartCount >= maxRestarts) {
                        log('达到最大重启次数，停止识别', 'warning');
                    }
                }
            };

            recognition.onerror = function(event) {
                log(`识别错误: ${event.error}`, 'error');
                
                if (event.error === 'no-speech') {
                    log('未检测到语音，将自动重启', 'warning');
                } else if (event.error === 'not-allowed') {
                    log('麦克风权限被拒绝', 'error');
                    shouldRestart = false;
                }
            };

            return true;
        }

        function startRecognition() {
            if (!recognition && !initRecognition()) {
                return;
            }

            shouldRestart = true;
            restartCount = 0;
            startTime = Date.now();
            updateRestartCount();
            
            log('开始语音识别测试', 'info');
            
            try {
                recognition.start();
            } catch (error) {
                log(`启动失败: ${error.message}`, 'error');
            }
        }

        function stopRecognition() {
            shouldRestart = false;
            
            if (recognition && isListening) {
                recognition.stop();
                log('手动停止识别', 'info');
            }
            
            if (durationTimer) {
                clearInterval(durationTimer);
                durationTimer = null;
            }
            
            startTime = null;
            updateStatus('已停止', false);
        }

        function extendRecognition() {
            if (!recognition || !isListening) return;
            
            log('手动延长识别时间', 'info');
            
            try {
                // 先停止当前识别
                recognition.stop();
                
                // 短暂延迟后重新开始
                setTimeout(() => {
                    if (shouldRestart) {
                        recognition.start();
                        log('识别已手动重启', 'success');
                    }
                }, 100);
            } catch (error) {
                log(`延长识别失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后初始化
        window.onload = function() {
            log('页面加载完成，准备测试语音识别', 'info');
            updateStatus('待机中', false);
        };
    </script>
</body>
</html>
