<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS音频播放测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .log {
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 TTS音频播放修复测试</h1>
        
        <div class="test-section">
            <h3>1. 音频权限检查</h3>
            <button onclick="checkAudioPermission()">检查音频权限</button>
            <button onclick="requestAudioPermission()">请求音频权限</button>
            <div id="permission-status" class="status info">点击按钮检查音频权限状态</div>
        </div>

        <div class="test-section">
            <h3>2. TTS参数转换测试</h3>
            <button onclick="testParameterConversion()">测试参数转换</button>
            <div id="param-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>3. Edge TTS API测试</h3>
            <button onclick="testEdgeTTS()">测试默认参数</button>
            <button onclick="testEdgeTTSWithParams()">测试自定义参数</button>
            <div id="tts-status" class="status info">点击按钮测试TTS API</div>
            <div id="tts-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>4. 音频播放测试</h3>
            <button onclick="testAudioPlayback()" id="play-btn">播放测试音频</button>
            <button onclick="stopAudioPlayback()" id="stop-btn" disabled>停止播放</button>
            <div id="audio-status" class="status info">点击按钮测试音频播放</div>
        </div>

        <div class="test-section">
            <h3>5. 完整流程测试</h3>
            <input type="text" id="test-text" placeholder="输入要合成的文字" value="你好，这是语音合成测试。" style="width: 300px; padding: 8px; margin-right: 10px;">
            <button onclick="testFullFlow()">完整流程测试</button>
            <div id="full-status" class="status info">输入文字并点击按钮进行完整流程测试</div>
        </div>
    </div>

    <script>
        let audioContext = null;
        let currentAudioSource = null;

        // 日志函数
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.innerHTML += `[${timestamp}] ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }

        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
        }

        // 1. 音频权限检查
        async function checkAudioPermission() {
            try {
                if (!window.AudioContext && !window.webkitAudioContext) {
                    setStatus('permission-status', '❌ 浏览器不支持Web Audio API', 'error');
                    return;
                }

                const AudioContextClass = window.AudioContext || window.webkitAudioContext;
                const testContext = new AudioContextClass();
                
                if (testContext.state === 'suspended') {
                    setStatus('permission-status', '⚠️ 需要用户交互来启用音频播放', 'error');
                } else {
                    setStatus('permission-status', '✅ 音频权限正常', 'success');
                }
                
                testContext.close();
            } catch (error) {
                setStatus('permission-status', `❌ 权限检查失败: ${error.message}`, 'error');
            }
        }

        async function requestAudioPermission() {
            try {
                const AudioContextClass = window.AudioContext || window.webkitAudioContext;
                audioContext = new AudioContextClass();
                
                if (audioContext.state === 'suspended') {
                    await audioContext.resume();
                }
                
                // 播放静音音频激活权限
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                gainNode.gain.value = 0;
                oscillator.frequency.value = 440;
                oscillator.start();
                oscillator.stop(audioContext.currentTime + 0.1);
                
                setTimeout(() => {
                    if (audioContext.state === 'running') {
                        setStatus('permission-status', '✅ 音频权限已获取', 'success');
                    } else {
                        setStatus('permission-status', '❌ 音频权限获取失败', 'error');
                    }
                }, 200);
                
            } catch (error) {
                setStatus('permission-status', `❌ 权限请求失败: ${error.message}`, 'error');
            }
        }

        // 2. 参数转换测试
        function testParameterConversion() {
            const testCases = [
                { pitch: 1.0, speed: 1.0, name: '默认值' },
                { pitch: 1.5, speed: 1.5, name: '中等值' },
                { pitch: 2.0, speed: 2.0, name: '最大值' },
            ];
            
            document.getElementById('param-log').innerHTML = '';
            log('param-log', '=== 参数转换测试 ===');
            
            testCases.forEach(testCase => {
                const pitchConverted = Math.round((testCase.pitch - 1) * 50);
                const rateConverted = Math.round((testCase.speed - 1) * 100);
                
                log('param-log', `${testCase.name}: pitch=${testCase.pitch} -> ${pitchConverted}Hz, speed=${testCase.speed} -> ${rateConverted}%`);
            });
        }

        // 3. Edge TTS API测试
        async function testEdgeTTS() {
            setStatus('tts-status', '🔄 正在测试TTS API...', 'info');
            document.getElementById('tts-log').innerHTML = '';
            
            try {
                const response = await fetch('/api/voice/edge/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        input: '你好，这是默认参数测试。',
                        options: {
                            voice: 'zh-CN-XiaoxiaoNeural',
                            rate: 0,
                            pitch: 0
                        }
                    })
                });

                if (response.ok) {
                    const audioData = await response.arrayBuffer();
                    log('tts-log', `✅ TTS API调用成功，音频大小: ${audioData.byteLength} bytes`);
                    setStatus('tts-status', '✅ TTS API测试成功', 'success');
                } else {
                    const errorText = await response.text();
                    log('tts-log', `❌ TTS API调用失败: HTTP ${response.status}`);
                    log('tts-log', `错误信息: ${errorText}`);
                    setStatus('tts-status', `❌ TTS API测试失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log('tts-log', `❌ TTS API调用异常: ${error.message}`);
                setStatus('tts-status', `❌ TTS API测试异常: ${error.message}`, 'error');
            }
        }

        async function testEdgeTTSWithParams() {
            setStatus('tts-status', '🔄 正在测试自定义参数...', 'info');
            
            try {
                const response = await fetch('/api/voice/edge/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        input: '你好，这是自定义参数测试。',
                        options: {
                            voice: 'zh-CN-XiaoxiaoNeural',
                            rate: 50,  // 对应speed=1.5
                            pitch: 25  // 对应pitch=1.5
                        }
                    })
                });

                if (response.ok) {
                    const audioData = await response.arrayBuffer();
                    log('tts-log', `✅ 自定义参数测试成功，音频大小: ${audioData.byteLength} bytes`);
                    setStatus('tts-status', '✅ 自定义参数测试成功', 'success');
                } else {
                    const errorText = await response.text();
                    log('tts-log', `❌ 自定义参数测试失败: HTTP ${response.status}`);
                    setStatus('tts-status', `❌ 自定义参数测试失败`, 'error');
                }
            } catch (error) {
                log('tts-log', `❌ 自定义参数测试异常: ${error.message}`);
                setStatus('tts-status', `❌ 自定义参数测试异常`, 'error');
            }
        }

        // 4. 音频播放测试
        async function testAudioPlayback() {
            if (!audioContext) {
                await requestAudioPermission();
            }

            if (!audioContext || audioContext.state !== 'running') {
                setStatus('audio-status', '❌ 音频上下文未就绪，请先获取音频权限', 'error');
                return;
            }

            setStatus('audio-status', '🔄 正在播放测试音频...', 'info');
            document.getElementById('play-btn').disabled = true;
            document.getElementById('stop-btn').disabled = false;

            try {
                // 生成测试音频（440Hz正弦波，持续2秒）
                const sampleRate = audioContext.sampleRate;
                const duration = 2;
                const frameCount = sampleRate * duration;
                const audioBuffer = audioContext.createBuffer(1, frameCount, sampleRate);
                const channelData = audioBuffer.getChannelData(0);

                for (let i = 0; i < frameCount; i++) {
                    channelData[i] = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 0.1;
                }

                currentAudioSource = audioContext.createBufferSource();
                currentAudioSource.buffer = audioBuffer;
                currentAudioSource.connect(audioContext.destination);
                
                currentAudioSource.onended = () => {
                    setStatus('audio-status', '✅ 音频播放完成', 'success');
                    document.getElementById('play-btn').disabled = false;
                    document.getElementById('stop-btn').disabled = true;
                    currentAudioSource = null;
                };

                currentAudioSource.start();
                setStatus('audio-status', '🔊 正在播放音频...', 'info');

            } catch (error) {
                setStatus('audio-status', `❌ 音频播放失败: ${error.message}`, 'error');
                document.getElementById('play-btn').disabled = false;
                document.getElementById('stop-btn').disabled = true;
            }
        }

        function stopAudioPlayback() {
            if (currentAudioSource) {
                currentAudioSource.stop();
                currentAudioSource = null;
                setStatus('audio-status', '⏹️ 音频播放已停止', 'info');
                document.getElementById('play-btn').disabled = false;
                document.getElementById('stop-btn').disabled = true;
            }
        }

        // 5. 完整流程测试
        async function testFullFlow() {
            const text = document.getElementById('test-text').value.trim();
            if (!text) {
                setStatus('full-status', '❌ 请输入要合成的文字', 'error');
                return;
            }

            setStatus('full-status', '🔄 正在进行完整流程测试...', 'info');

            try {
                // 1. 检查音频权限
                if (!audioContext) {
                    await requestAudioPermission();
                }

                if (!audioContext || audioContext.state !== 'running') {
                    setStatus('full-status', '❌ 音频权限未获取', 'error');
                    return;
                }

                // 2. 调用TTS API
                const response = await fetch('/api/voice/edge/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        input: text,
                        options: {
                            voice: 'zh-CN-XiaoxiaoNeural',
                            rate: 0,
                            pitch: 0
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`TTS API调用失败: HTTP ${response.status}`);
                }

                // 3. 获取音频数据
                const audioData = await response.arrayBuffer();
                
                // 4. 播放音频
                const audioBuffer = await audioContext.decodeAudioData(audioData);
                const source = audioContext.createBufferSource();
                source.buffer = audioBuffer;
                source.connect(audioContext.destination);
                
                source.onended = () => {
                    setStatus('full-status', '✅ 完整流程测试成功！', 'success');
                };

                source.start();
                setStatus('full-status', '🔊 正在播放合成的语音...', 'info');

            } catch (error) {
                setStatus('full-status', `❌ 完整流程测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查权限
        window.onload = function() {
            checkAudioPermission();
        };
    </script>
</body>
</html>
