#!/usr/bin/env python3
"""
测试TTS语音合成功能修复
"""

import os
import sys
import django
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'virtual_character_platform.settings')
django.setup()

def test_edge_tts_installation():
    """测试Edge TTS库是否安装"""
    print("🧪 测试Edge TTS库安装...")
    
    try:
        import edge_tts
        print("✅ Edge TTS库已安装")
        return True
    except ImportError as e:
        print(f"❌ Edge TTS库未安装: {e}")
        return False

def test_edge_tts_api():
    """测试Edge TTS API"""
    print("\n🧪 测试Edge TTS API...")
    
    try:
        from django.test import Client
        from django.contrib.auth.models import User
        
        # 创建测试客户端
        client = Client()
        
        # 测试数据
        test_data = {
            'input': '你好，我是神里绫华，很高兴见到你。',
            'options': {
                'voice': 'zh-CN-XiaoxiaoNeural',
                'rate': 0,
                'pitch': 0,
                'style': 'general'
            }
        }
        
        # 发送请求
        response = client.post(
            '/api/voice/edge/',
            data=test_data,
            content_type='application/json'
        )
        
        print(f"📊 API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            content_type = response.get('Content-Type', '')
            content_length = len(response.content)
            print(f"✅ Edge TTS API测试成功")
            print(f"   Content-Type: {content_type}")
            print(f"   Content-Length: {content_length} bytes")
            
            # 保存测试音频文件
            if content_length > 0:
                with open('test_tts_output.mp3', 'wb') as f:
                    f.write(response.content)
                print(f"   测试音频已保存为: test_tts_output.mp3")
            
            return True
        else:
            try:
                error_data = response.json()
                print(f"❌ Edge TTS API测试失败: {error_data}")
            except:
                print(f"❌ Edge TTS API测试失败: {response.content.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Edge TTS API测试失败: {e}")
        return False

def test_tts_voices_api():
    """测试TTS语音列表API"""
    print("\n🧪 测试TTS语音列表API...")
    
    try:
        from django.test import Client
        
        # 创建测试客户端
        client = Client()
        
        # 发送请求
        response = client.get('/api/voice/edge/voices')
        
        print(f"📊 API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            voices_data = response.json()
            voice_count = len(voices_data.get('data', []))
            print(f"✅ TTS语音列表API测试成功")
            print(f"   可用语音数量: {voice_count}")
            
            # 显示前几个中文语音
            chinese_voices = [v for v in voices_data.get('data', []) if 'zh-CN' in v.get('value', '')][:5]
            if chinese_voices:
                print("   中文语音示例:")
                for voice in chinese_voices:
                    print(f"     - {voice.get('label', 'Unknown')}: {voice.get('value', 'Unknown')}")
            
            return True
        else:
            print(f"❌ TTS语音列表API测试失败: {response.content.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ TTS语音列表API测试失败: {e}")
        return False

def test_direct_edge_tts():
    """直接测试Edge TTS功能"""
    print("\n🧪 直接测试Edge TTS功能...")
    
    try:
        import edge_tts
        import asyncio
        
        async def test_tts():
            text = "你好，我是神里绫华，很高兴见到你。"
            voice = "zh-CN-XiaoxiaoNeural"
            
            communicate = edge_tts.Communicate(text, voice)
            audio_data = b""
            
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]
            
            return audio_data
        
        # 运行异步测试
        audio_data = asyncio.run(test_tts())
        
        if audio_data and len(audio_data) > 0:
            print(f"✅ 直接Edge TTS测试成功")
            print(f"   音频数据长度: {len(audio_data)} bytes")
            
            # 保存测试音频文件
            with open('test_direct_tts.mp3', 'wb') as f:
                f.write(audio_data)
            print(f"   测试音频已保存为: test_direct_tts.mp3")
            
            return True
        else:
            print("❌ 直接Edge TTS测试失败: 无音频数据")
            return False
            
    except Exception as e:
        print(f"❌ 直接Edge TTS测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始TTS语音合成功能测试\n")
    
    # 测试Edge TTS库安装
    installation_ok = test_edge_tts_installation()
    
    # 测试Edge TTS API
    api_ok = test_edge_tts_api()
    
    # 测试TTS语音列表API
    voices_ok = test_tts_voices_api()
    
    # 直接测试Edge TTS
    direct_ok = test_direct_edge_tts()
    
    # 总结
    print(f"\n📋 测试结果总结:")
    print(f"   Edge TTS库安装: {'✅ 通过' if installation_ok else '❌ 失败'}")
    print(f"   Edge TTS API: {'✅ 通过' if api_ok else '❌ 失败'}")
    print(f"   TTS语音列表API: {'✅ 通过' if voices_ok else '❌ 失败'}")
    print(f"   直接Edge TTS: {'✅ 通过' if direct_ok else '❌ 失败'}")
    
    if installation_ok and api_ok and voices_ok and direct_ok:
        print("\n🎉 所有TTS测试通过！语音合成功能正常！")
    else:
        print("\n⚠️ 部分TTS测试失败，需要进一步调试。")
        
        if not installation_ok:
            print("\n💡 修复建议:")
            print("   请运行: pip install edge-tts")

if __name__ == '__main__':
    main()
