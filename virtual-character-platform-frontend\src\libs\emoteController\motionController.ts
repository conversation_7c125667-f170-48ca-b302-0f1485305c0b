import { VRM } from '@pixiv/three-vrm';
import { AnimationA<PERSON>, AnimationClip, AnimationMixer, LoopO<PERSON>, LoopRepeat } from 'three';

import { loadMixamoAnimation } from '../FBXAnimation/loadMixamoAnimation';
import { loadVMDAnimation } from '../VMDAnimation/loadVMDAnimation';
import IKHandler from '../VMDAnimation/vrm-ik-handler';
import VRMIKHandler from '../VMDAnimation/vrm-ik-handler';
import { loadVRMAnimation } from '../VRMAnimation/loadVRMAnimation';

import { motionPresetMap, MotionFileType, type MotionPresetName } from './motionPresetMap';

export class MotionController {
  private vrm: VRM;
  private mixer?: AnimationMixer;
  private currentAction?: AnimationAction;
  private currentClip?: AnimationClip;
  private ikHandler: VRMIKHandler;
  private preloadedMotions = new Map<string, AnimationClip>();

  constructor(vrm: VRM) {
    this.vrm = vrm;
    this.ikHandler = IKHandler.get(vrm);
  }

  public async preloadMotion(motion: MotionPresetName) {
    const { type, url } = this.getMotionInfo(motion);
    if (url) {
      await this.preloadMotionUrl(type, url);
    }
  }

  public async preloadMotionUrl(fileType: MotionFileType, url: string) {
    if (!this.preloadedMotions.has(url)) {
      const clip = await this.loadMotionClip(fileType, url);
      if (clip) {
        this.preloadedMotions.set(url, clip);
      }
    }
  }

  public playMotion(motion: MotionPresetName, loop: boolean = true) {
    const motionConfig = this.getMotionInfo(motion);
    if (motionConfig.url) {
      console.log(`播放动作文件: ${motion} (${motionConfig.url})`);
      this.playMotionUrl(motionConfig.type, motionConfig.url, loop);
    } else {
      // 如果没有URL，使用程序化动画作为后备
      console.log(`播放程序化动作: ${motion}`);
      this.playProceduralMotion(motion, loop);
    }
  }

  /**
   * 播放程序化动作
   */
  private playProceduralMotion(motion: MotionPresetName, loop: boolean = true) {
    switch (motion) {
      case 'idle':
        this.playIdleMotion();
        break;
      case 'talking':
        this.playTalkingMotion();
        break;
      case 'greeting':
      case 'hello':
        this.playGreetingMotion();
        break;
      case 'happy':
        this.playHappyMotion();
        break;
      case 'thinking':
        this.playThinkingMotion();
        break;
      default:
        console.log(`暂不支持程序化动作: ${motion}`);
        // 默认播放idle动作
        this.playIdleMotion();
        break;
    }
  }

  /**
   * 播放idle动作 - 自然的双臂垂落姿态
   */
  private playIdleMotion() {
    if (!this.vrm.humanoid) return;

    try {
      // 重置所有骨骼到自然姿态
      const humanoid = this.vrm.humanoid;

      // 设置双臂自然垂落 - 更自然的角度
      const leftUpperArm = humanoid.getNormalizedBoneNode('leftUpperArm');
      const rightUpperArm = humanoid.getNormalizedBoneNode('rightUpperArm');
      const leftLowerArm = humanoid.getNormalizedBoneNode('leftLowerArm');
      const rightLowerArm = humanoid.getNormalizedBoneNode('rightLowerArm');
      const leftHand = humanoid.getNormalizedBoneNode('leftHand');
      const rightHand = humanoid.getNormalizedBoneNode('rightHand');

      // 上臂：自然下垂，稍微向外张开
      if (leftUpperArm) {
        leftUpperArm.rotation.set(0.15, 0, 0.2); // X轴向前倾斜，Z轴向外张开
      }
      if (rightUpperArm) {
        rightUpperArm.rotation.set(0.15, 0, -0.2); // X轴向前倾斜，Z轴向外张开
      }

      // 下臂：自然弯曲，模拟重力作用
      if (leftLowerArm) {
        leftLowerArm.rotation.set(0.3, 0, 0); // 更明显的弯曲
      }
      if (rightLowerArm) {
        rightLowerArm.rotation.set(0.3, 0, 0); // 更明显的弯曲
      }

      // 手部：自然放松
      if (leftHand) {
        leftHand.rotation.set(0.1, 0, 0.1); // 轻微弯曲
      }
      if (rightHand) {
        rightHand.rotation.set(0.1, 0, -0.1); // 轻微弯曲
      }

      // 设置脊柱自然姿态
      const spine = humanoid.getNormalizedBoneNode('spine');
      const chest = humanoid.getNormalizedBoneNode('chest');
      if (spine) {
        spine.rotation.set(0.05, 0, 0); // 轻微向前倾
      }
      if (chest) {
        chest.rotation.set(0.02, 0, 0); // 胸部轻微前倾
      }

      // 设置头部自然姿态
      const head = humanoid.getNormalizedBoneNode('head');
      const neck = humanoid.getNormalizedBoneNode('neck');
      if (neck) {
        neck.rotation.set(0.03, 0, 0); // 脖子轻微前倾
      }
      if (head) {
        head.rotation.set(0.08, 0, 0); // 头部轻微向下看
      }

      // 设置腿部自然站立姿态
      const leftUpperLeg = humanoid.getNormalizedBoneNode('leftUpperLeg');
      const rightUpperLeg = humanoid.getNormalizedBoneNode('rightUpperLeg');
      const leftLowerLeg = humanoid.getNormalizedBoneNode('leftLowerLeg');
      const rightLowerLeg = humanoid.getNormalizedBoneNode('rightLowerLeg');

      if (leftUpperLeg) {
        leftUpperLeg.rotation.set(0, 0, 0); // 大腿保持直立
      }
      if (rightUpperLeg) {
        rightUpperLeg.rotation.set(0, 0, 0); // 大腿保持直立
      }
      if (leftLowerLeg) {
        leftLowerLeg.rotation.set(0.05, 0, 0); // 小腿轻微弯曲
      }
      if (rightLowerLeg) {
        rightLowerLeg.rotation.set(0.05, 0, 0); // 小腿轻微弯曲
      }

      console.log('✅ 已应用增强的自然idle姿态');
    } catch (error) {
      console.error('应用idle姿态失败:', error);
    }
  }

  /**
   * 播放talking动作 - 说话时的自然姿态
   */
  private playTalkingMotion() {
    if (!this.vrm.humanoid) return;

    try {
      // 在idle基础上添加说话时的微调
      this.playIdleMotion();

      // 头部稍微抬起
      const head = this.vrm.humanoid.getNormalizedBoneNode('head');
      if (head) {
        head.rotation.set(0.02, 0, 0); // 比idle稍微抬起一点
      }

      console.log('✅ 已应用talking姿态');
    } catch (error) {
      console.error('应用talking姿态失败:', error);
    }
  }

  /**
   * 播放greeting动作 - 打招呼的手势
   */
  private playGreetingMotion() {
    if (!this.vrm.humanoid) return;

    try {
      // 先应用idle姿态作为基础
      this.playIdleMotion();

      const humanoid = this.vrm.humanoid;

      // 右手挥手动作
      const rightUpperArm = humanoid.getNormalizedBoneNode('rightUpperArm');
      const rightLowerArm = humanoid.getNormalizedBoneNode('rightLowerArm');
      const rightHand = humanoid.getNormalizedBoneNode('rightHand');

      if (rightUpperArm) {
        rightUpperArm.rotation.set(-0.5, 0, -0.8); // 右臂抬起
      }
      if (rightLowerArm) {
        rightLowerArm.rotation.set(0.8, 0, 0); // 前臂弯曲
      }
      if (rightHand) {
        rightHand.rotation.set(0.2, 0, 0.3); // 手部稍微弯曲
      }

      // 头部稍微转向右侧
      const head = humanoid.getNormalizedBoneNode('head');
      if (head) {
        head.rotation.set(0.05, 0.1, 0); // 头部稍微转向右侧
      }

      console.log('✅ 已应用greeting姿态');
    } catch (error) {
      console.error('应用greeting姿态失败:', error);
    }
  }

  /**
   * 播放happy动作 - 开心的姿态
   */
  private playHappyMotion() {
    if (!this.vrm.humanoid) return;

    try {
      // 先应用idle姿态作为基础
      this.playIdleMotion();

      const humanoid = this.vrm.humanoid;

      // 双手稍微张开，表示开心
      const leftUpperArm = humanoid.getNormalizedBoneNode('leftUpperArm');
      const rightUpperArm = humanoid.getNormalizedBoneNode('rightUpperArm');

      if (leftUpperArm) {
        leftUpperArm.rotation.set(0.1, 0, 0.4); // 左臂稍微张开
      }
      if (rightUpperArm) {
        rightUpperArm.rotation.set(0.1, 0, -0.4); // 右臂稍微张开
      }

      // 头部稍微抬起
      const head = humanoid.getNormalizedBoneNode('head');
      if (head) {
        head.rotation.set(-0.05, 0, 0); // 头部稍微抬起
      }

      console.log('✅ 已应用happy姿态');
    } catch (error) {
      console.error('应用happy姿态失败:', error);
    }
  }

  /**
   * 播放thinking动作 - 思考的姿态
   */
  private playThinkingMotion() {
    if (!this.vrm.humanoid) return;

    try {
      // 先应用idle姿态作为基础
      this.playIdleMotion();

      const humanoid = this.vrm.humanoid;

      // 右手托腮的思考姿态
      const rightUpperArm = humanoid.getNormalizedBoneNode('rightUpperArm');
      const rightLowerArm = humanoid.getNormalizedBoneNode('rightLowerArm');
      const rightHand = humanoid.getNormalizedBoneNode('rightHand');

      if (rightUpperArm) {
        rightUpperArm.rotation.set(-0.3, 0, -0.5); // 右臂抬起
      }
      if (rightLowerArm) {
        rightLowerArm.rotation.set(1.2, 0, 0); // 前臂大幅弯曲
      }
      if (rightHand) {
        rightHand.rotation.set(0.3, 0, 0.2); // 手部弯曲
      }

      // 头部稍微向右倾斜
      const head = humanoid.getNormalizedBoneNode('head');
      if (head) {
        head.rotation.set(0.1, 0.15, 0.1); // 头部向右倾斜
      }

      console.log('✅ 已应用thinking姿态');
    } catch (error) {
      console.error('应用thinking姿态失败:', error);
    }
  }

  private getMotionInfo(motion: MotionPresetName) {
    return motionPresetMap[motion] || motionPresetMap['idle'];
  }

  public async playMotionUrl(
    fileType: MotionFileType,
    url: string,
    loop: boolean = true,
  ): Promise<void> {
    this.stopMotion();

    let clip: AnimationClip | undefined;

    if (this.preloadedMotions.has(url)) {
      clip = this.preloadedMotions.get(url);
    } else {
      clip = await this.loadMotionClip(fileType, url);
    }

    if (!clip) {
      console.error(`无法加载动作: ${url}`);
      return;
    }

    // 保存VRM模型的原始材质状态
    const originalMaterials = new Map();
    this.vrm.scene.traverse((child) => {
      if (child.type === 'Mesh' && (child as any).material) {
        const mesh = child as any;

        // 保存当前的真实材质状态
        originalMaterials.set(mesh.uuid, {
          material: mesh.material.clone(),
          transparent: mesh.material.transparent,
          opacity: mesh.material.opacity,
          alphaTest: mesh.material.alphaTest,
        });
      }
    });

    // 创建新的 mixer，但只针对骨骼动画，不影响材质
    this.mixer = new AnimationMixer(this.vrm.scene);

    this.currentAction = this.mixer.clipAction(clip);
    this.currentAction.setLoop(loop ? LoopRepeat : LoopOnce, loop ? Infinity : 1);

    // 设置动画播放完成后的回调，恢复材质状态
    this.currentAction.getMixer().addEventListener('finished', () => {
      console.log('🎭 动画播放完成，恢复VRM材质状态');
      this.restoreVRMMaterials(originalMaterials);
    });

    this.currentAction.play();

    // 立即恢复材质状态，防止动画加载时的材质覆盖
    requestAnimationFrame(() => {
      this.restoreVRMMaterials(originalMaterials);
    });

    this.currentClip = clip;
  }

  /**
   * 恢复VRM模型的原始材质状态
   */
  private restoreVRMMaterials(originalMaterials: Map<string, any>): void {
    this.vrm.scene.traverse((child) => {
      if (child.type === 'Mesh' && originalMaterials.has(child.uuid)) {
        const mesh = child as any;
        const originalState = originalMaterials.get(child.uuid);

        // 恢复材质属性，确保VRM模型保持正确的渲染状态
        if (mesh.material && originalState) {
          mesh.material.transparent = originalState.transparent;
          mesh.material.opacity = originalState.opacity;
          mesh.material.alphaTest = originalState.alphaTest;
          mesh.material.needsUpdate = true;
        }
      }
    });
  }

  private async loadMotionClip(
    fileType: MotionFileType,
    url: string,
  ): Promise<AnimationClip | undefined> {
    switch (fileType) {
      case 'vmd':
        return await this.loadVMD(url);
      case 'fbx':
        return await this.loadFBX(url);
      case 'vrma':
        return await this.loadVRMA(url);
      default:
        throw new Error('不支持的文件格式');
    }
  }

  private async loadVMD(url: string): Promise<AnimationClip | undefined> {
    return await loadVMDAnimation(url, this.vrm);
  }

  private async loadFBX(url: string): Promise<AnimationClip | undefined> {
    return await loadMixamoAnimation(url, this.vrm);
  }

  private async loadVRMA(url: string): Promise<AnimationClip | undefined> {
    return await loadVRMAnimation(url, this.vrm);
  }

  public stopMotion(): void {
    if (this.mixer) {
      this.mixer.stopAllAction();
      this.mixer.uncacheRoot(this.vrm.scene);
    }

    this.ikHandler.disableAll();

    if (this.currentAction) {
      this.currentAction.stop();
    }

    this.currentAction = undefined;
    this.currentClip = undefined;
    this.mixer = undefined;

    // 停止动作后，不需要额外的材质修复
  }



  /**
   * 重置到idle状态
   */
  public resetToIdle(): void {
    console.log('MotionController: 重置到idle状态');
    this.stopMotion();
    // 延迟一帧确保停止动作完成
    setTimeout(() => {
      this.playMotion('idle');
    }, 16);
  }

  /**
   * 强制应用idle姿态（不依赖动画系统）
   */
  public forceIdlePose(): void {
    console.log('MotionController: 强制应用idle姿态');
    this.stopMotion();
    this.playIdleMotion();
  }

  public update(delta: number): void {
    if (this.mixer) {
      this.mixer.update(delta);
    }
    this.vrm.update(delta);
    this.ikHandler.update();
  }

  // 添加与现有系统兼容的方法
  public getCurrentMotion(): MotionPresetName {
    // 返回当前播放的动作名称，如果没有则返回idle
    return this.currentClip?.name || 'idle';
  }

  public isPlaying(): boolean {
    return this.currentAction?.isRunning() || false;
  }

  public setMotionWeight(weight: number): void {
    if (this.currentAction) {
      this.currentAction.setEffectiveWeight(weight);
    }
  }

  public getMotionDuration(): number {
    return this.currentClip?.duration || 0;
  }

  public setMotionTime(time: number): void {
    if (this.currentAction) {
      this.currentAction.time = time;
    }
  }
}
