import json
import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.http import HttpResponse
import asyncio
import sys
import os
import threading
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class EdgeTTSView(APIView):
    """
    Edge TTS语音合成API视图
    """
    permission_classes = []  # 暂时移除认证要求以便调试

    def post(self, request):
        """
        使用Edge TTS进行语音合成
        """
        try:
            # 获取请求数据 - 兼容DRF和普通Django请求
            if hasattr(request, 'data'):
                # DRF请求
                data = request.data
            else:
                # 普通Django请求
                import json
                data = json.loads(request.body.decode('utf-8'))

            input_text = data.get('input', '')
            options = data.get('options', {})

            logger.info(f"Edge TTS请求 - 文本: {input_text[:50]}..., 选项: {options}")

            if not input_text or not input_text.strip():
                return Response({
                    'error': '输入文本不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 导入Edge TTS库
            try:
                import edge_tts
            except ImportError as e:
                logger.error(f"Edge TTS库未安装: {str(e)}")
                return Response({
                    'error': 'Edge TTS服务不可用，请安装edge-tts库'
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

            # 获取语音参数
            voice = options.get('voice', 'zh-CN-XiaoxiaoNeural')
            rate = options.get('rate', 0)  # 前端传来的是0-100的百分比
            pitch = options.get('pitch', 0)  # 前端传来的是0-50的百分比
            style = options.get('style', 'general')

            # 转换参数格式为Edge TTS需要的格式
            # rate: 0-100 -> +0% to +100%
            rate_str = f"+{rate}%" if rate >= 0 else f"{rate}%"
            # pitch: 0-50 -> +0Hz to +50Hz
            pitch_str = f"+{pitch}Hz" if pitch >= 0 else f"{pitch}Hz"

            logger.info(f"Edge TTS参数 - 语音: {voice}, 语速: {rate_str}, 音调: {pitch_str}")

            # 使用线程池执行异步操作
            def run_async_tts():
                try:
                    # 创建新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    async def generate_speech():
                        try:
                            # 创建Edge TTS通信对象
                            communicate = edge_tts.Communicate(
                                input_text,
                                voice,
                                rate=rate_str,
                                pitch=pitch_str
                            )

                            audio_data = b""
                            async for chunk in communicate.stream():
                                if chunk["type"] == "audio":
                                    audio_data += chunk["data"]
                            return audio_data
                        except Exception as e:
                            logger.error(f"Edge TTS通信失败: {str(e)}")
                            # 如果是403错误，可能是网络限制，尝试使用备用方案
                            if "403" in str(e) or "Invalid response status" in str(e):
                                logger.warning("Edge TTS服务访问受限，尝试使用本地TTS备用方案")
                                return self._fallback_tts(input_text)
                            raise e

                    # 运行异步函数
                    audio_data = loop.run_until_complete(generate_speech())
                    loop.close()
                    return audio_data
                except Exception as e:
                    logger.error(f"异步TTS执行失败: {str(e)}")
                    # 如果Edge TTS失败，返回一个简单的错误音频或者使用备用方案
                    raise e

            # 在线程池中执行
            try:
                with ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(run_async_tts)
                    audio_data = future.result(timeout=30)  # 30秒超时
            except Exception as e:
                logger.error(f"Edge TTS生成失败: {str(e)}")
                # 尝试使用备用TTS
                try:
                    logger.info("尝试使用备用TTS方案")
                    audio_data = self._fallback_tts(input_text)
                except Exception as fallback_error:
                    logger.error(f"备用TTS也失败: {str(fallback_error)}")
                    return Response({
                        'error': f'语音生成失败: {str(e)}。备用方案也失败: {str(fallback_error)}'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            if not audio_data:
                return Response({
                    'error': '语音生成失败，返回数据为空'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            logger.info(f"Edge TTS生成成功，音频大小: {len(audio_data)} bytes")

            # 返回音频数据
            response = HttpResponse(audio_data, content_type='audio/mpeg')
            response['Content-Length'] = len(audio_data)
            response['Cache-Control'] = 'no-cache'
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
            return response

        except Exception as e:
            logger.error(f"Edge TTS API错误: {str(e)}")
            return Response({
                'error': f'服务器内部错误: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _fallback_tts(self, text):
        """备用TTS方案，使用pyttsx3生成语音"""
        try:
            # 检查pyttsx3是否可用
            try:
                import pyttsx3
            except ImportError:
                logger.error("pyttsx3模块未安装，跳过备用TTS，返回静音音频")
                return self._generate_error_audio()

            import tempfile
            import os

            logger.info("使用pyttsx3备用TTS方案")

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name

            # 初始化pyttsx3引擎
            engine = pyttsx3.init()

            # 设置语音属性
            voices = engine.getProperty('voices')
            if voices:
                # 尝试找到中文语音
                for voice in voices:
                    if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                        engine.setProperty('voice', voice.id)
                        break

            # 设置语速和音量
            engine.setProperty('rate', 200)  # 语速
            engine.setProperty('volume', 0.9)  # 音量

            # 保存到临时文件
            engine.save_to_file(text, temp_path)
            engine.runAndWait()

            # 读取音频数据
            if os.path.exists(temp_path):
                with open(temp_path, 'rb') as f:
                    audio_data = f.read()
                os.unlink(temp_path)  # 删除临时文件
                return audio_data
            else:
                raise Exception("备用TTS生成失败")

        except Exception as e:
            logger.error(f"备用TTS失败: {str(e)}")
            # 返回一个简单的错误提示音频（可以是预录制的）
            return self._generate_error_audio()

    def _generate_error_audio(self):
        """生成错误提示音频（简单的静音音频）"""
        # 生成1秒的静音WAV文件
        import struct

        # WAV文件头
        sample_rate = 22050
        duration = 1  # 1秒
        num_samples = sample_rate * duration

        # WAV文件格式
        wav_header = struct.pack('<4sI4s4sIHHIIHH4sI',
            b'RIFF', 36 + num_samples * 2, b'WAVE', b'fmt ', 16,
            1, 1, sample_rate, sample_rate * 2, 2, 16, b'data', num_samples * 2)

        # 静音数据
        silence = b'\x00\x00' * num_samples

        return wav_header + silence

    def options(self, request):
        """处理CORS预检请求"""
        response = HttpResponse()
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        return response


class MicrosoftTTSView(APIView):
    """
    Microsoft TTS语音合成API视图
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """
        使用Microsoft TTS进行语音合成
        """
        try:
            # 获取请求数据
            data = request.data
            input_text = data.get('input', '')
            options = data.get('options', {})
            
            logger.info(f"Microsoft TTS请求 - 文本: {input_text[:50]}..., 选项: {options}")
            
            if not input_text:
                return Response({
                    'error': '输入文本不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 这里可以集成Microsoft Azure Speech Services
            # 目前返回错误，提示需要配置
            return Response({
                'error': 'Microsoft TTS服务需要配置Azure Speech Services'
            }, status=status.HTTP_501_NOT_IMPLEMENTED)
            
        except Exception as e:
            logger.error(f"Microsoft TTS API错误: {str(e)}")
            return Response({
                'error': f'服务器内部错误: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class VoiceListView(APIView):
    """
    获取可用语音列表
    """
    permission_classes = []  # 暂时移除认证要求以便调试
    
    def get(self, request):
        """
        获取Edge TTS可用语音列表
        """
        try:
            # 导入Edge TTS库
            try:
                import edge_tts
            except ImportError:
                logger.error("Edge TTS库未安装")
                return Response({
                    'error': 'Edge TTS服务不可用'
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
            
            # 异步获取语音列表
            async def get_voices():
                voices = await edge_tts.list_voices()
                return voices
            
            # 运行异步函数
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                voices = loop.run_until_complete(get_voices())
                loop.close()
            except Exception as e:
                logger.error(f"获取语音列表失败: {str(e)}")
                return Response({
                    'error': f'获取语音列表失败: {str(e)}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # 过滤中文语音并格式化为前端期望的格式
            chinese_voices = [
                {
                    'label': voice['FriendlyName'],
                    'value': voice['ShortName'],
                    'locale': voice['Locale'],
                    'gender': voice['Gender']
                }
                for voice in voices
                if voice['Locale'].startswith('zh-')
            ]

            logger.info(f"返回 {len(chinese_voices)} 个中文语音")

            return Response({
                'data': chinese_voices
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"语音列表API错误: {str(e)}")
            return Response({
                'error': f'服务器内部错误: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
