# AI提示词系统问题分析和修复计划

## 🔍 问题概述

在项目中发现了多个与AI提示词相关的问题，主要集中在硬编码角色数据和服务架构不一致方面。

## 🚨 核心问题清单

### 1. 神里绫华硬编码问题 (高优先级)

#### 1.1 问题描述
- **现象**: 更换VRM模型后仍使用神里绫华的角色设定
- **根因**: 多处硬编码的神里绫华配置数据

#### 1.2 具体问题位置

**前端硬编码文件**:
- `virtual-character-platform-frontend/src/data/sampleVRMCharacters.ts` (64-80行)
  ```typescript
  // 🚨 神里绫华硬编码配置
  createVRMCharacterConfig(
    'ayaka-sample',
    '神里绫华',
    '原神角色，优雅的白鹭公主，社奉行神里家的大小姐',
    // ...
    {
      systemRole: '请你扮演游戏《原神》中一个叫做神里绫华的角色，并以绫华的语气和习惯来和我对话。你应该保持用"绫华"而不是"我"来称呼你自己。你和我对话时必须全程用"旅行者"来称呼我。',
    }
  )
  ```

**后端角色数据获取逻辑**:
- `core/views.py` (310-335行) - ChatMessageView中的提示词构建
  ```python
  # 🚨 可能使用了错误的角色数据源
  base_dialogue_prompt = f"You are {character.name}, a virtual character talking to the user {request.user.username}. " \
                        f"You are {character.age} years old."
  ```

**测试和演示脚本** (需要清理):
- `check_character_data.py` - 创建神里绫华角色的脚本
- `create_test_characters.py` - 测试角色数据
- `demo_spark_4_ultra.py` - 演示用角色提示词构建
- `VRM模型集成工具.js` - 推荐模型列表

### 2. AI服务架构不一致问题 (中优先级)

#### 2.1 问题描述
- **现象**: AI服务提示词传递使用了过时的WebSocket服务
- **当前状态**: 项目已切换到HTTPS服务，但部分代码仍使用WebSocket

#### 2.2 具体问题位置

**过时的WebSocket服务**:
- `backend-services/services/spark_chat_service.py` (111-153行)
  ```python
  # 🚨 使用WebSocket服务 (已过时)
  response = self._send_websocket_request(messages)
  ```

- `backend-services/services/spark_dialogue_service.py` (111-153行)
  ```python
  # 🚨 使用WebSocket服务 (已过时)  
  response = self._send_websocket_request(messages)
  ```

**当前使用的HTTPS服务**:
- 项目已切换到HTTPS API调用
- 需要统一服务调用方式

### 3. 提示词模板系统重复问题 (低优先级)

#### 3.1 问题描述
- **现象**: 存在多套提示词模板系统
- **影响**: 可能导致配置不一致

#### 3.2 具体位置
- 前端角色模板: `virtual-character-platform-frontend/src/components/role/tabs/RoleTab.tsx`
- 国际化模板: `locales/*/role.json`
- 后端模板系统: `backend-services/services/prompt_engineering/`
- Django模板模型: `core/models.py` (PromptTemplate)

## 🛠️ 修复计划

### 阶段1: 清理测试脚本 (立即执行)
- [x] 删除过时的测试和演示脚本
- [x] 保留核心功能文件
- [x] 记录问题点供后续修复

### 阶段2: 修复硬编码问题 (高优先级)
- [ ] 分析前端角色数据流转逻辑
- [ ] 修复ChatMessageView中的角色数据获取
- [ ] 实现动态角色配置系统
- [ ] 移除硬编码的神里绫华配置

### 阶段3: 统一AI服务架构 (中优先级)
- [ ] 分析当前HTTPS服务实现
- [ ] 重构WebSocket服务为HTTPS服务
- [ ] 统一AI服务调用接口
- [ ] 更新相关文档

### 阶段4: 整合提示词模板系统 (低优先级)
- [ ] 分析各套模板系统的功能
- [ ] 设计统一的模板架构
- [ ] 迁移和整合现有模板
- [ ] 建立模板管理机制

## 📋 待删除的测试脚本清单

### 角色相关测试脚本
- `check_character_data.py` - 神里绫华角色创建脚本
- `create_test_characters.py` - 测试角色数据脚本
- `demo_spark_4_ultra.py` - 星火AI演示脚本

### VRM模型相关脚本
- `VRM模型集成工具.js` - VRM模型集成工具

### TTS相关测试脚本
- `test_tts_fixes.py` - TTS修复测试脚本
- `test_edge_tts_fix.py` - Edge TTS修复测试脚本
- `debug_chat_issues.py` - 聊天问题调试脚本
- `debug_voice_flow.py` - 语音流程调试脚本
- `test_fixes.py` - 综合修复测试脚本

## 🔧 技术债务记录

### 1. 服务架构债务
- **WebSocket vs HTTPS**: 需要统一AI服务调用方式
- **重复服务**: `spark_chat_service.py` 和 `spark_dialogue_service.py` 功能重复

### 2. 配置管理债务
- **硬编码配置**: 多处硬编码的角色和模型配置
- **配置分散**: 配置信息分散在多个文件中

### 3. 模板系统债务
- **多套模板**: 前端、后端、国际化多套模板系统
- **模板不一致**: 不同模板系统可能存在配置差异

## 📅 修复时间线

- **立即**: 清理测试脚本，记录问题
- **本周**: 修复神里绫华硬编码问题
- **下周**: 分析和设计AI服务架构统一方案
- **后续**: 根据优先级逐步修复其他问题

## 📝 注意事项

1. **保持向后兼容**: 修复过程中确保现有功能正常工作
2. **测试验证**: 每个修复都需要充分测试
3. **文档更新**: 修复后及时更新相关文档
4. **代码审查**: 重要修改需要代码审查

---

**创建时间**: 2025-07-20  
**状态**: 问题分析完成，待执行修复计划  
**优先级**: 高 (神里绫华硬编码) > 中 (AI服务架构) > 低 (模板系统)
