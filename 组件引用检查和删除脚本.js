// 组件引用检查和删除脚本
// 在项目根目录运行: node 组件引用检查和删除脚本.js

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 要检查和删除的组件列表
const COMPONENTS_TO_DELETE = {
  // 高优先级删除 - 确认无引用
  high: [
    { name: 'Analytics', path: 'src/components/Analytics', type: 'directory' },
    { name: 'BrandWatermark', path: 'src/components/BrandWatermark', type: 'directory' },
    { name: 'Branding', path: 'src/components/Branding', type: 'directory' },
    { name: 'Logo', path: 'src/components/Logo', type: 'directory' },
    { name: 'TopBanner', path: 'src/components/TopBanner', type: 'directory' },
    { name: 'HolographicCard', path: 'src/components/HolographicCard', type: 'directory' },
    { name: 'DanceInfo', path: 'src/components/DanceInfo', type: 'directory' },
    { name: 'RomanceCarousel', path: 'src/components/RomanceCarousel', type: 'directory' },
    { name: 'VRMModelCard', path: 'src/components/VRMModelCard', type: 'directory' },
    { name: 'ModelIcon', path: 'src/components/ModelIcon', type: 'directory' },
    { name: 'ModelSelect', path: 'src/components/ModelSelect', type: 'directory' },
    { name: 'NProgress', path: 'src/components/NProgress', type: 'directory' },
    { name: 'VoiceSelector', path: 'src/components/VoiceSelector.tsx', type: 'file' },
    { name: 'Application', path: 'src/components/Application', type: 'directory' }
  ],
  
  // 中优先级删除 - 需要确认
  medium: [
    { name: 'ChatItem_Legacy', path: 'src/components/ChatItem_Legacy', type: 'directory' },
    { name: 'Error', path: 'src/components/Error', type: 'directory' },
    { name: 'Menu', path: 'src/components/Menu', type: 'directory' },
    { name: 'PanelTitle', path: 'src/components/PanelTitle', type: 'directory' },
    { name: 'RoleCard', path: 'src/components/RoleCard', type: 'directory' },
    { name: 'TextArea', path: 'src/components/TextArea', type: 'directory' },
    { name: 'server', path: 'src/components/server', type: 'directory' }
  ],
  
  // 低优先级删除 - 谨慎删除
  low: [
    { name: 'Author', path: 'src/components/Author', type: 'directory' },
    { name: 'StopLoading', path: 'src/components/StopLoading.tsx', type: 'file' },
    { name: 'NetworkStatusMonitor', path: 'src/components/NetworkStatusMonitor.tsx', type: 'file' },
    { name: 'GlobalErrorHandler', path: 'src/components/GlobalErrorHandler.tsx', type: 'file' },
    { name: 'ErrorRecovery', path: 'src/components/ErrorRecovery.tsx', type: 'file' },
    { name: 'OptimizedImage', path: 'src/components/OptimizedImage.tsx', type: 'file' },
    { name: 'SkeletonList', path: 'src/components/SkeletonList.tsx', type: 'file' },
    { name: 'CharacterVoicePlayer', path: 'src/components/CharacterVoicePlayer.tsx', type: 'file' },
    { name: 'GridList', path: 'src/components/GridList', type: 'directory' },
    { name: 'ListItem', path: 'src/components/ListItem', type: 'directory' }
  ]
};

// 检查组件是否被引用
function checkComponentReferences(componentName) {
  console.log(`🔍 检查组件引用: ${componentName}`);
  
  const searchPatterns = [
    `import.*${componentName}`,
    `from.*${componentName}`,
    `'.*${componentName}'`,
    `".*${componentName}"`,
    `<${componentName}`,
    `${componentName}\\s*\\(`
  ];
  
  const references = [];
  
  for (const pattern of searchPatterns) {
    try {
      const result = execSync(`grep -r "${pattern}" src/ --include="*.tsx" --include="*.ts" --include="*.js" --include="*.jsx" || true`, 
        { encoding: 'utf8' });
      
      if (result.trim()) {
        const lines = result.trim().split('\n');
        references.push(...lines);
      }
    } catch (error) {
      // 忽略grep错误
    }
  }
  
  // 过滤掉组件自身的定义
  const filteredReferences = references.filter(ref => {
    return !ref.includes(`components/${componentName}/`) && 
           !ref.includes(`components/${componentName}.tsx`) &&
           !ref.includes(`${componentName}/index.tsx`);
  });
  
  return {
    hasReferences: filteredReferences.length > 0,
    references: filteredReferences
  };
}

// 创建备份目录
function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
  const backupDir = `backup_components_${timestamp}`;
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  return backupDir;
}

// 备份并删除组件
function backupAndDelete(component, backupDir) {
  const { name, path: componentPath, type } = component;
  
  if (!fs.existsSync(componentPath)) {
    console.log(`⚠️ 组件不存在: ${componentPath}`);
    return false;
  }
  
  try {
    // 创建备份
    const backupPath = path.join(backupDir, name);
    if (type === 'directory') {
      execSync(`cp -r "${componentPath}" "${backupPath}"`);
      execSync(`rm -rf "${componentPath}"`);
    } else {
      execSync(`cp "${componentPath}" "${backupPath}"`);
      execSync(`rm "${componentPath}"`);
    }
    
    console.log(`✅ 已删除: ${componentPath}`);
    return true;
  } catch (error) {
    console.error(`❌ 删除失败: ${componentPath}`, error.message);
    return false;
  }
}

// 运行构建测试
function runBuildTest() {
  console.log('\n🧪 运行构建测试...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ 构建测试通过');
    return true;
  } catch (error) {
    console.error('❌ 构建测试失败');
    return false;
  }
}

// 运行类型检查
function runTypeCheck() {
  console.log('\n🔍 运行类型检查...');
  try {
    execSync('npm run type-check', { stdio: 'inherit' });
    console.log('✅ 类型检查通过');
    return true;
  } catch (error) {
    console.error('❌ 类型检查失败');
    return false;
  }
}

// 主函数
async function main() {
  console.log('🚀 开始组件引用检查和删除流程...\n');
  
  // 创建备份目录
  const backupDir = createBackup();
  console.log(`💾 备份目录: ${backupDir}\n`);
  
  // 第一阶段：检查高优先级组件
  console.log('🔥 第一阶段：检查高优先级组件');
  console.log('=' .repeat(50));
  
  const highPriorityResults = [];
  
  for (const component of COMPONENTS_TO_DELETE.high) {
    const result = checkComponentReferences(component.name);
    
    if (result.hasReferences) {
      console.log(`❌ ${component.name} 仍有引用:`);
      result.references.forEach(ref => console.log(`   ${ref}`));
      highPriorityResults.push({ component, canDelete: false, references: result.references });
    } else {
      console.log(`✅ ${component.name} 无引用，可以安全删除`);
      highPriorityResults.push({ component, canDelete: true, references: [] });
    }
  }
  
  // 删除高优先级组件
  console.log('\n🗑️ 开始删除高优先级组件...');
  let deletedCount = 0;
  
  for (const result of highPriorityResults) {
    if (result.canDelete) {
      if (backupAndDelete(result.component, backupDir)) {
        deletedCount++;
      }
    } else {
      console.log(`⚠️ 跳过删除: ${result.component.name} (仍有引用)`);
    }
  }
  
  console.log(`\n📊 第一阶段完成，已删除 ${deletedCount} 个组件`);
  
  // 运行构建测试
  if (deletedCount > 0) {
    const buildSuccess = runBuildTest();
    const typeCheckSuccess = runTypeCheck();
    
    if (!buildSuccess || !typeCheckSuccess) {
      console.log('\n❌ 测试失败，请检查删除的组件是否被意外引用');
      console.log(`💾 可从备份目录恢复: ${backupDir}`);
      return;
    }
  }
  
  // 询问是否继续第二阶段
  console.log('\n🤔 是否继续第二阶段删除 (中优先级组件)?');
  console.log('警告: 这些组件可能被使用，删除前请确认！');
  
  // 这里可以添加用户输入确认逻辑
  // 为了自动化，暂时跳过第二阶段
  console.log('⏸️ 暂停在第一阶段，请手动确认后继续');
  
  // 生成报告
  generateReport(highPriorityResults, backupDir, deletedCount);
}

// 生成删除报告
function generateReport(results, backupDir, deletedCount) {
  const reportPath = path.join(backupDir, 'deletion_report.md');
  
  let report = `# 组件删除报告\n\n`;
  report += `**删除时间**: ${new Date().toLocaleString()}\n`;
  report += `**备份目录**: ${backupDir}\n`;
  report += `**已删除组件数**: ${deletedCount}\n\n`;
  
  report += `## 删除结果\n\n`;
  
  for (const result of results) {
    const status = result.canDelete ? '✅ 已删除' : '❌ 跳过';
    report += `- **${result.component.name}**: ${status}\n`;
    
    if (result.references.length > 0) {
      report += `  - 引用位置:\n`;
      result.references.forEach(ref => {
        report += `    - \`${ref}\`\n`;
      });
    }
    report += '\n';
  }
  
  report += `## 后续建议\n\n`;
  report += `1. 检查构建和类型检查是否通过\n`;
  report += `2. 测试所有主要功能页面\n`;
  report += `3. 如有问题，从备份目录恢复\n`;
  report += `4. 确认无问题后可删除备份目录\n`;
  
  fs.writeFileSync(reportPath, report);
  console.log(`\n📝 删除报告已生成: ${reportPath}`);
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  checkComponentReferences,
  COMPONENTS_TO_DELETE
};
