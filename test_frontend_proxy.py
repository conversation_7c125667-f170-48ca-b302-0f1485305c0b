#!/usr/bin/env python3
"""
测试前端代理的聊天API
"""
import requests
import json

def test_proxy_chat():
    """测试前端代理的聊天功能"""
    print("🌐 测试前端代理聊天API...")
    
    # 1. 先通过代理登录获取token
    login_data = {
        "username": "api_test_user",
        "password": "test_password_123"
    }
    
    try:
        # 通过前端代理登录
        login_response = requests.post(
            'http://localhost:5173/api/auth/login/',
            json=login_data,
            timeout=10
        )
        
        print(f"🔐 代理登录响应: {login_response.status_code}")
        
        if login_response.status_code == 200:
            token = login_response.json().get('token')
            print(f"✅ 通过代理获取到token: {token[:20]}...")
            
            # 2. 测试代理的聊天API
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            chat_data = {
                "messages": [
                    {"role": "user", "content": "你好，这是通过前端代理的测试"}
                ],
                "model": "4.0Ultra",
                "stream": False
            }
            
            chat_response = requests.post(
                'http://localhost:5173/api/chat/spark/',
                json=chat_data,
                headers=headers,
                timeout=30
            )
            
            print(f"💬 代理聊天API响应: {chat_response.status_code}")
            
            if chat_response.status_code == 200:
                result = chat_response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', 'N/A')
                print(f"✅ 通过代理的AI回复: {content[:100]}...")
                print("🎉 前端代理工作正常！")
                return True
            else:
                print(f"❌ 代理聊天API失败: {chat_response.text}")
                return False
                
        else:
            print(f"❌ 代理登录失败: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 代理测试失败: {e}")
        return False

def test_character_chat_proxy():
    """测试角色聊天代理"""
    print("\n👤 测试角色聊天代理...")
    
    # 登录获取token
    login_data = {
        "username": "api_test_user",
        "password": "test_password_123"
    }
    
    try:
        login_response = requests.post(
            'http://localhost:5173/api/auth/login/',
            json=login_data
        )
        
        if login_response.status_code == 200:
            token = login_response.json().get('token')
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            # 测试角色聊天
            character_data = {
                "user_message": "你好，这是通过前端代理的角色聊天测试",
                "enable_tts": False,
                "voice_mode": False
            }
            
            response = requests.post(
                'http://localhost:5173/api/characters/1/chat/',
                json=character_data,
                headers=headers,
                timeout=30
            )
            
            print(f"👤 角色聊天代理响应: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 角色回复: {result.get('character_response', 'N/A')[:100]}...")
                return True
            else:
                print(f"❌ 角色聊天代理失败: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 角色聊天代理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 前端代理功能测试")
    print("=" * 50)
    
    # 测试通用聊天代理
    chat_success = test_proxy_chat()
    
    # 测试角色聊天代理
    character_success = test_character_chat_proxy()
    
    print("\n" + "=" * 50)
    print("📊 代理测试总结")
    print("=" * 50)
    
    if chat_success and character_success:
        print("🎉 所有代理功能测试通过！")
        print("✅ 前端可以正常通过代理调用后端API")
        print("✅ 聊天功能应该可以正常工作")
    else:
        print("⚠️ 部分代理功能有问题")
        if not chat_success:
            print("❌ 通用聊天代理失败")
        if not character_success:
            print("❌ 角色聊天代理失败")

if __name__ == "__main__":
    main()
