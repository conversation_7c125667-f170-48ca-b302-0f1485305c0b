#!/usr/bin/env python3
"""
测试前端情感分析功能是否能正常调用后端API
模拟前端发送的请求格式
"""

import requests
import json
import time

def test_emotion_analysis_api():
    """测试情感分析API调用"""
    print("🧠 测试前端情感分析功能")
    
    # 后端API地址 (注意：前端运行在5174，但API请求会被代理到8000)
    base_url = "http://localhost:5174"  # 前端地址，会被Vite代理
    
    # 首先登录获取token
    print("\n1️⃣ 登录获取认证token...")
    
    login_url = f"{base_url}/api/auth/login/"
    login_data = {
        "username": "api_test_user",
        "password": "testpass123"
    }
    
    try:
        login_response = requests.post(
            login_url,
            json=login_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            access_token = login_result.get('token')
            print(f"✅ 登录成功，获取到token")
        else:
            print(f"⚠️ 登录失败: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False
    
    # 测试情感分析API
    print("\n2️⃣ 测试情感分析API...")
    
    # 这是前端analyzeEmotion函数会发送的请求格式
    chat_url = f"{base_url}/api/chat/spark/"  # 注意：添加尾部斜杠
    
    # 模拟前端情感分析请求的数据格式
    emotion_analysis_data = {
        "model": "spark-4.0-ultra",
        "messages": [
            {
                "role": "system",
                "content": """你是一个专业的情感分析师，请分析用户输入的情感状态，并返回JSON格式的分析结果。

请按照以下格式返回结果：
{
  "emotion": "情感类型(如: joy, sadness, anger, fear, surprise, neutral等)",
  "intensity": "情感强度(1-10的数字)",
  "expression": "建议的表情(如: happy, sad, angry, surprised, neutral等)",
  "motion": "建议的动作(如: Wave, Idle, Dance等)",
  "speechStyle": "语音风格(如: normal, excited, calm, sad等)",
  "duration": "表现持续时间(1-10秒)",
  "reason": "分析原因的简短说明"
}

请确保返回的是有效的JSON格式。"""
            },
            {
                "role": "user",
                "content": "我今天特别开心，因为终于完成了这个重要的项目！感觉所有的努力都值得了！"
            }
        ],
        "stream": False,
        "max_tokens": 1000,
        "temperature": 0.3
    }
    
    # 设置请求头
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {access_token}'
    }
    
    try:
        print(f"📤 发送情感分析请求到: {chat_url}")
        
        response = requests.post(
            chat_url,
            json=emotion_analysis_data,
            headers=headers,
            timeout=30
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ 情感分析API调用成功!")
            
            # 提取AI回复内容
            if 'choices' in response_data and len(response_data['choices']) > 0:
                ai_content = response_data['choices'][0]['message']['content']
                print(f"🤖 AI分析结果: {ai_content}")
                
                # 尝试解析JSON结果
                try:
                    emotion_result = json.loads(ai_content)
                    print("📊 解析后的情感分析结果:")
                    for key, value in emotion_result.items():
                        print(f"   {key}: {value}")
                    return True
                except json.JSONDecodeError:
                    print("⚠️ AI返回的不是有效JSON格式，但API调用成功")
                    return True
            else:
                print("⚠️ 响应格式异常")
                return False
                
        elif response.status_code == 404:
            print("❌ API路径不存在 - 这就是原来的404错误！")
            print("现在应该已经修复了")
            return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_character_chat_api():
    """测试角色聊天API（对比测试）"""
    print("\n3️⃣ 对比测试：角色聊天API...")
    
    base_url = "http://localhost:5174"
    
    # 登录
    login_url = f"{base_url}/api/auth/login/"
    login_data = {
        "username": "api_test_user",
        "password": "testpass123"
    }
    
    try:
        login_response = requests.post(login_url, json=login_data, headers={'Content-Type': 'application/json'})
        if login_response.status_code == 200:
            access_token = login_response.json().get('token')
        else:
            print("❌ 登录失败")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 测试角色聊天API
    character_chat_url = f"{base_url}/api/characters/1/chat/"
    chat_data = {
        "user_message": "你好，今天心情怎么样？",
        "enable_tts": False,
        "voice_mode": False
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {access_token}'
    }
    
    try:
        response = requests.post(character_chat_url, json=chat_data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            print("✅ 角色聊天API正常工作")
            response_data = response.json()
            print(f"🤖 角色回复: {response_data.get('character_response', 'N/A')}")
            return True
        else:
            print(f"❌ 角色聊天API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 角色聊天API异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试前端情感分析功能")
    print("=" * 60)
    
    # 测试情感分析API
    emotion_test_result = test_emotion_analysis_api()
    
    # 对比测试角色聊天API
    character_test_result = test_character_chat_api()
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"   情感分析API: {'✅ 通过' if emotion_test_result else '❌ 失败'}")
    print(f"   角色聊天API: {'✅ 通过' if character_test_result else '❌ 失败'}")
    
    if emotion_test_result:
        print("\n🎉 情感分析功能修复成功！")
        print("   前端的analyzeEmotion函数现在应该能正常工作了")
        print("   沉浸式聊天页面的404错误应该已经解决")
    else:
        print("\n⚠️ 情感分析功能仍有问题，需要进一步调试")
