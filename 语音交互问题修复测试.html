<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音交互问题修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .test-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .log-area {
            background: #f6f6f6;
            border: 1px solid #d9d9d9;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .warning { color: #faad14; }
        .info { color: #1890ff; }
    </style>
</head>
<body>
    <h1>🔧 语音交互问题修复测试</h1>
    
    <div class="test-section">
        <h2>📋 问题描述</h2>
        <p><strong>现象</strong>: AI回复后没有语音播放，后端显示EdgeTTS通信失败</p>
        <p><strong>原因</strong>: 
            <br>1. 前端收到audio_url但没有播放逻辑
            <br>2. 参数转换可能有问题 (pitch=1→0Hz, speed=1→0%)
            <br>3. Edge TTS WebSocket连接被拒绝(403错误)
        </p>
    </div>

    <div class="test-section">
        <h2>🧪 测试1: 参数转换验证</h2>
        <button class="test-button" onclick="testParameterConversion()">测试参数转换</button>
        <div id="param-result" class="log-area"></div>
    </div>

    <div class="test-section">
        <h2>🧪 测试2: 后端TTS API测试</h2>
        <button class="test-button" onclick="testBackendTTS()">测试后端TTS</button>
        <div id="tts-result" class="log-area"></div>
    </div>

    <div class="test-section">
        <h2>🧪 测试3: 音频URL播放测试</h2>
        <button class="test-button" onclick="testAudioPlayback()">测试音频播放</button>
        <div id="audio-result" class="log-area"></div>
    </div>

    <div class="test-section">
        <h2>🧪 测试4: 完整流程测试</h2>
        <button class="test-button" onclick="testFullFlow()">测试完整流程</button>
        <div id="full-result" class="log-area"></div>
    </div>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        // 测试1: 参数转换验证
        function testParameterConversion() {
            clearLog('param-result');
            log('param-result', '开始测试参数转换...', 'info');
            
            const testCases = [
                { pitch: 1, speed: 1, desc: '默认值' },
                { pitch: 1.5, speed: 1.5, desc: '中等值' },
                { pitch: 2, speed: 2, desc: '最大值' },
                { pitch: 0.5, speed: 0.5, desc: '最小值' }
            ];
            
            testCases.forEach(testCase => {
                const pitchResult = Math.round((testCase.pitch - 1) * 50);
                const speedResult = Math.round((testCase.speed - 1) * 100);
                
                log('param-result', 
                    `${testCase.desc}: pitch=${testCase.pitch} → ${pitchResult}Hz, speed=${testCase.speed} → ${speedResult}%`, 
                    pitchResult === 0 && speedResult === 0 ? 'warning' : 'success'
                );
            });
            
            log('param-result', '✅ 参数转换测试完成', 'success');
        }

        // 测试2: 后端TTS API测试
        async function testBackendTTS() {
            clearLog('tts-result');
            log('tts-result', '开始测试后端TTS API...', 'info');
            
            const payload = {
                input: '这是一个测试消息',
                options: {
                    voice: 'zh-CN-XiaoxiaoNeural',
                    pitch: 0,  // 1.0 → 0Hz
                    rate: 0,   // 1.0 → 0%
                    style: 'general'
                }
            };
            
            log('tts-result', `请求payload: ${JSON.stringify(payload, null, 2)}`, 'info');
            
            try {
                const response = await fetch('/api/voice/edge/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(payload)
                });
                
                log('tts-result', `响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const audioBlob = await response.blob();
                    log('tts-result', `音频数据大小: ${audioBlob.size} bytes`, 'success');
                    log('tts-result', '✅ 后端TTS API测试成功', 'success');
                } else {
                    const errorText = await response.text();
                    log('tts-result', `错误信息: ${errorText}`, 'error');
                }
            } catch (error) {
                log('tts-result', `请求失败: ${error.message}`, 'error');
            }
        }

        // 测试3: 音频URL播放测试
        async function testAudioPlayback() {
            clearLog('audio-result');
            log('audio-result', '开始测试音频播放...', 'info');
            
            // 模拟后端返回的音频URL
            const testAudioUrl = 'http://localhost:8000/media/audio/tts_test.mp3';
            
            try {
                log('audio-result', `测试音频URL: ${testAudioUrl}`, 'info');
                
                const audio = new Audio(testAudioUrl);
                
                audio.onloadstart = () => {
                    log('audio-result', '开始加载音频...', 'info');
                };
                
                audio.oncanplay = () => {
                    log('audio-result', '音频可以播放', 'success');
                    audio.play().then(() => {
                        log('audio-result', '音频开始播放', 'success');
                    }).catch(err => {
                        log('audio-result', `播放失败: ${err.message}`, 'error');
                    });
                };
                
                audio.onended = () => {
                    log('audio-result', '✅ 音频播放完成', 'success');
                };
                
                audio.onerror = (error) => {
                    log('audio-result', `音频加载错误: ${error}`, 'error');
                };
                
                // 设置音频源
                audio.src = testAudioUrl;
                audio.load();
                
            } catch (error) {
                log('audio-result', `音频播放测试失败: ${error.message}`, 'error');
            }
        }

        // 测试4: 完整流程测试
        async function testFullFlow() {
            clearLog('full-result');
            log('full-result', '开始完整流程测试...', 'info');
            
            try {
                // 步骤1: 模拟AI聊天请求
                log('full-result', '步骤1: 发送聊天请求...', 'info');
                
                const chatResponse = await fetch('/api/characters/28/chat/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: '今天天气怎么样？',
                        enable_tts: true,
                        voice_mode: 'zh-CN-XiaoxiaoNeural'
                    })
                });
                
                if (!chatResponse.ok) {
                    throw new Error(`聊天请求失败: ${chatResponse.status}`);
                }
                
                const chatData = await chatResponse.json();
                log('full-result', `AI响应: ${JSON.stringify(chatData, null, 2)}`, 'success');
                
                // 步骤2: 检查是否有audio_url
                if (chatData.audio_url) {
                    log('full-result', '步骤2: 检测到音频URL，开始播放...', 'info');
                    
                    const audio = new Audio(chatData.audio_url);
                    
                    audio.oncanplay = () => {
                        log('full-result', '音频可以播放，开始播放...', 'success');
                        audio.play();
                    };
                    
                    audio.onended = () => {
                        log('full-result', '✅ 完整流程测试成功！', 'success');
                    };
                    
                    audio.onerror = () => {
                        log('full-result', '音频播放失败，尝试前端TTS...', 'warning');
                        // 这里可以添加前端TTS逻辑
                    };
                    
                    audio.src = chatData.audio_url;
                    audio.load();
                    
                } else {
                    log('full-result', '步骤2: 没有音频URL，需要前端TTS', 'warning');
                }
                
            } catch (error) {
                log('full-result', `完整流程测试失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
