import React, { useCallback, useRef, useEffect } from 'react';
import { message } from 'antd';

/**
 * 触摸拦截配置
 */
export interface TouchInterceptionConfig {
  disabled: boolean;
  showWarning: boolean;
  warningMessage: string;
  preventBubbling: boolean;
  logInterceptions: boolean;
}

/**
 * 触摸事件信息
 */
export interface TouchEventInfo {
  type: string;
  timestamp: number;
  clientX: number;
  clientY: number;
  target: EventTarget | null;
  intercepted: boolean;
}

/**
 * 触摸拦截回调
 */
export interface TouchInterceptionCallbacks {
  onTouchIntercepted?: (eventInfo: TouchEventInfo) => void;
  onTouchAllowed?: (eventInfo: TouchEventInfo) => void;
  onInterceptionStateChange?: (disabled: boolean) => void;
}

/**
 * 组件属性
 */
export interface TouchInteractionWrapperProps {
  children: React.ReactNode;
  disabled?: boolean;
  showWarning?: boolean;
  warningMessage?: string;
  preventBubbling?: boolean;
  logInterceptions?: boolean;
  className?: string;
  style?: React.CSSProperties;
  callbacks?: TouchInterceptionCallbacks;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: TouchInterceptionConfig = {
  disabled: false,
  showWarning: true,
  warningMessage: '语音交互进行中，触摸功能暂时不可用',
  preventBubbling: true,
  logInterceptions: true,
};

/**
 * 触摸交互包装组件
 * 
 * 功能：
 * - 拦截触摸事件（当disabled=true时）
 * - 显示用户友好的提示信息
 * - 记录拦截日志
 * - 支持事件冒泡控制
 * - 提供详细的事件信息回调
 */
export const TouchInteractionWrapper: React.FC<TouchInteractionWrapperProps> = ({
  children,
  disabled = false,
  showWarning = true,
  warningMessage = DEFAULT_CONFIG.warningMessage,
  preventBubbling = true,
  logInterceptions = true,
  className,
  style,
  callbacks,
}) => {
  const wrapperRef = useRef<HTMLDivElement>(null);
  const lastWarningTime = useRef<number>(0);
  const interceptionCount = useRef<number>(0);

  /**
   * 记录日志
   */
  const log = useCallback((message: string, level: 'info' | 'warn' | 'error' = 'info') => {
    if (!logInterceptions) return;
    
    const timestamp = new Date().toLocaleTimeString();
    const prefix = `[TouchInterceptionWrapper ${timestamp}]`;
    
    switch (level) {
      case 'warn':
        console.warn(prefix, message);
        break;
      case 'error':
        console.error(prefix, message);
        break;
      default:
        console.log(prefix, message);
    }
  }, [logInterceptions]);

  /**
   * 创建事件信息对象
   */
  const createEventInfo = useCallback((event: React.TouchEvent | React.MouseEvent, intercepted: boolean): TouchEventInfo => {
    const clientX = 'touches' in event ? event.touches[0]?.clientX || 0 : event.clientX;
    const clientY = 'touches' in event ? event.touches[0]?.clientY || 0 : event.clientY;
    
    return {
      type: event.type,
      timestamp: Date.now(),
      clientX,
      clientY,
      target: event.target,
      intercepted,
    };
  }, []);

  /**
   * 显示警告消息（带防抖）
   */
  const showWarningMessage = useCallback(() => {
    const now = Date.now();
    // 防抖：1秒内只显示一次警告
    if (now - lastWarningTime.current < 1000) {
      return;
    }
    
    lastWarningTime.current = now;
    message.warning(warningMessage);
    log(`显示警告消息: ${warningMessage}`, 'warn');
  }, [warningMessage, log]);

  /**
   * 处理触摸开始事件
   */
  const handleTouchStart = useCallback((event: React.TouchEvent) => {
    const eventInfo = createEventInfo(event, disabled);
    
    if (disabled) {
      if (preventBubbling) {
        event.preventDefault();
        event.stopPropagation();
      }
      
      interceptionCount.current++;
      log(`拦截触摸开始事件 (第${interceptionCount.current}次)`, 'warn');
      
      if (showWarning) {
        showWarningMessage();
      }
      
      callbacks?.onTouchIntercepted?.(eventInfo);
      return;
    }
    
    log('允许触摸开始事件');
    callbacks?.onTouchAllowed?.(eventInfo);
  }, [disabled, preventBubbling, showWarning, log, showWarningMessage, callbacks, createEventInfo]);

  /**
   * 处理触摸移动事件
   */
  const handleTouchMove = useCallback((event: React.TouchEvent) => {
    const eventInfo = createEventInfo(event, disabled);
    
    if (disabled) {
      if (preventBubbling) {
        event.preventDefault();
        event.stopPropagation();
      }
      
      callbacks?.onTouchIntercepted?.(eventInfo);
      return;
    }
    
    callbacks?.onTouchAllowed?.(eventInfo);
  }, [disabled, preventBubbling, callbacks, createEventInfo]);

  /**
   * 处理触摸结束事件
   */
  const handleTouchEnd = useCallback((event: React.TouchEvent) => {
    const eventInfo = createEventInfo(event, disabled);
    
    if (disabled) {
      if (preventBubbling) {
        event.preventDefault();
        event.stopPropagation();
      }
      
      callbacks?.onTouchIntercepted?.(eventInfo);
      return;
    }
    
    callbacks?.onTouchAllowed?.(eventInfo);
  }, [disabled, preventBubbling, callbacks, createEventInfo]);

  /**
   * 处理鼠标按下事件（桌面端）
   */
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    const eventInfo = createEventInfo(event, disabled);
    
    if (disabled) {
      if (preventBubbling) {
        event.preventDefault();
        event.stopPropagation();
      }
      
      interceptionCount.current++;
      log(`拦截鼠标按下事件 (第${interceptionCount.current}次)`, 'warn');
      
      if (showWarning) {
        showWarningMessage();
      }
      
      callbacks?.onTouchIntercepted?.(eventInfo);
      return;
    }
    
    log('允许鼠标按下事件');
    callbacks?.onTouchAllowed?.(eventInfo);
  }, [disabled, preventBubbling, showWarning, log, showWarningMessage, callbacks, createEventInfo]);

  /**
   * 处理鼠标移动事件
   */
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!disabled) return;
    
    const eventInfo = createEventInfo(event, true);
    
    if (preventBubbling) {
      event.preventDefault();
      event.stopPropagation();
    }
    
    callbacks?.onTouchIntercepted?.(eventInfo);
  }, [disabled, preventBubbling, callbacks, createEventInfo]);

  /**
   * 处理鼠标抬起事件
   */
  const handleMouseUp = useCallback((event: React.MouseEvent) => {
    const eventInfo = createEventInfo(event, disabled);
    
    if (disabled) {
      if (preventBubbling) {
        event.preventDefault();
        event.stopPropagation();
      }
      
      callbacks?.onTouchIntercepted?.(eventInfo);
      return;
    }
    
    callbacks?.onTouchAllowed?.(eventInfo);
  }, [disabled, preventBubbling, callbacks, createEventInfo]);

  /**
   * 处理点击事件
   */
  const handleClick = useCallback((event: React.MouseEvent) => {
    const eventInfo = createEventInfo(event, disabled);
    
    if (disabled) {
      if (preventBubbling) {
        event.preventDefault();
        event.stopPropagation();
      }
      
      log('拦截点击事件', 'warn');
      callbacks?.onTouchIntercepted?.(eventInfo);
      return;
    }
    
    callbacks?.onTouchAllowed?.(eventInfo);
  }, [disabled, preventBubbling, log, callbacks, createEventInfo]);

  /**
   * 监听disabled状态变化
   */
  useEffect(() => {
    callbacks?.onInterceptionStateChange?.(disabled);
    log(`触摸拦截状态变更: ${disabled ? '启用' : '禁用'}`);
    
    if (disabled) {
      interceptionCount.current = 0;
    }
  }, [disabled, callbacks, log]);

  /**
   * 计算样式
   */
  const wrapperStyle: React.CSSProperties = {
    ...style,
    // 当禁用时，可以选择性地修改样式
    ...(disabled && {
      pointerEvents: preventBubbling ? 'none' : 'auto',
      cursor: 'not-allowed',
      opacity: 0.7,
    }),
  };

  return (
    <div
      ref={wrapperRef}
      className={className}
      style={wrapperStyle}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onClick={handleClick}
      data-touch-disabled={disabled}
      data-interception-count={interceptionCount.current}
    >
      {children}
    </div>
  );
};

export default TouchInteractionWrapper;
