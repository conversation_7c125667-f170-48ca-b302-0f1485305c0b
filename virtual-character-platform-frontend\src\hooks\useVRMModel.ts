import { useState, useEffect, useRef } from 'react';
import { App } from 'antd';
import { VRMStorageInfo } from '../types/vrm';
import { diagnoseVRMIssues } from '../utils/vrmDebugUtils';

interface UseVRMModelOptions {
  viewer?: any;
  onLoadSuccess?: (model: VRMStorageInfo) => void;
  onLoadError?: (error: Error, model: VRMStorageInfo) => void;
}

interface UseVRMModelReturn {
  vrmModelLoaded: boolean | null;
  loadVRMModel: (model: VRMStorageInfo) => Promise<void>;
  resetVRMModel: () => void;
}

/**
 * VRM模型管理Hook
 * 处理VRM模型的加载、状态管理和错误处理
 */
export const useVRMModel = (options: UseVRMModelOptions = {}): UseVRMModelReturn => {
  const { viewer, onLoadSuccess, onLoadError } = options;
  const { message } = App.useApp();
  const [vrmModelLoaded, setVrmModelLoaded] = useState<boolean | null>(null);
  const isMountedRef = useRef(true);

  /**
   * 加载VRM模型
   * @param model VRM模型信息
   */
  const loadVRMModel = async (model: VRMStorageInfo): Promise<void> => {
    if (!viewer || vrmModelLoaded !== null) {
      return;
    }

    let loadingMessage: any = null;

    try {
      console.log('🔄 开始加载VRM模型:', model.name);
      loadingMessage = message.loading(`正在加载VRM模型 ${model.name}...`, 0);

      // 使用viewer加载VRM模型
      await viewer.loadVrm(model.vrmModelUrl);

      if (!isMountedRef.current) return;

      if (loadingMessage) message.destroy();
      message.success(`✅ VRM模型 ${model.name} 加载成功！`);
      console.log('✅ VRM模型加载完成，模型已显示');



      setVrmModelLoaded(true);
      onLoadSuccess?.(model);

    } catch (error) {
      if (!isMountedRef.current) return;

      console.error('❌ 加载VRM模型失败:', error);
      if (loadingMessage) message.destroy();

      // 显示用户友好的错误信息
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      if (errorMessage.includes('ERR_CONNECTION_RESET') || errorMessage.includes('Failed to fetch')) {
        message.error('网络连接问题，无法加载VRM模型。请检查网络连接或稍后重试。', 5);
      } else {
        message.error(`加载VRM模型失败: ${errorMessage}`, 5);
      }

      setVrmModelLoaded(false);
      onLoadError?.(error instanceof Error ? error : new Error(errorMessage), model);
      console.log('⚠️ VRM模型加载失败，但将继续播放欢迎语音');
    }
  };

  /**
   * 重置VRM模型状态
   */
  const resetVRMModel = () => {
    setVrmModelLoaded(null);
    console.log('🔄 VRM模型状态已重置');
  };

  // 组件卸载时清理WebGL资源
  useEffect(() => {
    return () => {
      isMountedRef.current = false;

      // 清理WebGL上下文，防止内存泄漏
      if (viewer?.renderer) {
        try {
          console.log('🧹 清理WebGL上下文');
          viewer.renderer.dispose();
          viewer.renderer.forceContextLoss();
        } catch (error) {
          console.warn('清理WebGL上下文时出错:', error);
        }
      }
    };
  }, [viewer]);

  return {
    vrmModelLoaded,
    loadVRMModel,
    resetVRMModel,
  };
};

/**
 * 自动加载VRM模型的Hook
 * 在viewer和模型信息准备好后自动加载模型
 */
export const useAutoVRMLoader = (
  selectedVrmModel: VRMStorageInfo | null,
  viewer: any,
  options: UseVRMModelOptions = {}
) => {
  const { vrmModelLoaded, loadVRMModel } = useVRMModel(options);
  const loadedModelIdRef = useRef<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    let timeoutId: NodeJS.Timeout;

    // 只有在模型ID变化且未加载过时才加载
    if (selectedVrmModel && viewer && vrmModelLoaded === null &&
        loadedModelIdRef.current !== selectedVrmModel.id) {

      console.log('🔄 准备加载VRM模型:', selectedVrmModel.name);

      // 延迟加载，确保viewer已经初始化
      timeoutId = setTimeout(() => {
        if (isMounted && loadedModelIdRef.current !== selectedVrmModel.id) {
          loadedModelIdRef.current = selectedVrmModel.id;
          loadVRMModel(selectedVrmModel);
        }
      }, 1000);
    }

    return () => {
      isMounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [selectedVrmModel?.id, viewer, vrmModelLoaded]); // 只依赖模型ID，避免对象引用变化

  return { vrmModelLoaded };
};

/**
 * VRM模型诊断Hook
 * 对VRM模型进行健康检查
 */
export const useVRMDiagnosis = () => {
  const { message } = App.useApp();

  const diagnoseModel = async (model: VRMStorageInfo): Promise<string[]> => {
    try {
      const issues = await diagnoseVRMIssues(model);
      if (issues.length > 0) {
        console.warn('VRM模型诊断发现问题:', issues);
        message.warning(`VRM模型可能存在问题: ${issues[0]}`);
      } else {
        console.log('✅ VRM模型诊断通过');
      }
      return issues;
    } catch (error) {
      console.error('VRM模型诊断失败:', error);
      return ['诊断过程出错'];
    }
  };

  return { diagnoseModel };
};
