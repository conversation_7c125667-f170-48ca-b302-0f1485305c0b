#!/usr/bin/env python3
"""
测试流式响应的脚本
"""
import requests
import json

def test_stream_response():
    """测试流式响应"""
    
    # 1. 先登录获取token
    login_url = "http://localhost:8000/api/auth/login/"
    login_data = {
        "username": "api_test_user",
        "password": "test_password_123"
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
            
        token = login_response.json().get('token')
        if not token:
            print("❌ 未获取到token")
            return False
            
        print(f"✅ 登录成功，获取到token")
        
        # 2. 测试流式响应
        chat_url = "http://localhost:8000/api/chat/spark/"
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        test_data = {
            "model": "spark-4.0-ultra",
            "messages": [
                {
                    "role": "user",
                    "content": "你好，请简单介绍一下你自己"
                }
            ],
            "stream": True
        }
        
        print("📤 发送流式请求...")
        response = requests.post(chat_url, json=test_data, headers=headers, stream=True)
        
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 流式响应成功!")
            print("📝 响应内容:")
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    print(f"   {decoded_line}")
            return True
        else:
            print(f"❌ 流式响应失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试流式响应")
    print("=" * 50)
    test_stream_response()
