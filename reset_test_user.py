#!/usr/bin/env python3
"""
重置测试用户密码
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'virtual_character_platform.settings')
django.setup()

from django.contrib.auth.models import User

try:
    user = User.objects.get(username='api_test_user')
    user.set_password('test_password_123')
    user.save()
    print('✅ 测试用户密码重置成功')
except User.DoesNotExist:
    user = User.objects.create_user(
        username='api_test_user',
        password='test_password_123',
        email='<EMAIL>'
    )
    print('✅ 测试用户创建成功')
except Exception as e:
    print(f'❌ 操作失败: {e}')
