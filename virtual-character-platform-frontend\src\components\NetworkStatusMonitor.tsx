import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Button, Modal, Typography, Space, Tag, Divider } from 'antd';
import { 
  WifiOutlined, 
  DisconnectOutlined, 
  ReloadOutlined, 
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface NetworkStatus {
  isOnline: boolean;
  connectionType?: string;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
}

interface NetworkError {
  type: 'connection_reset' | 'dns_failure' | 'timeout' | 'unknown';
  domain?: string;
  message: string;
  timestamp: number;
}

interface NetworkStatusMonitorProps {
  onNetworkChange?: (status: NetworkStatus) => void;
  showDetailedInfo?: boolean;
}

const NetworkStatusMonitor: React.FC<NetworkStatusMonitorProps> = ({
  onNetworkChange,
  showDetailedInfo = false
}) => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine
  });
  const [networkErrors, setNetworkErrors] = useState<NetworkError[]>([]);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showStatusDetails, setShowStatusDetails] = useState(false);

  // 监控网络状态变化
  useEffect(() => {
    const updateNetworkStatus = () => {
      const connection = (navigator as any).connection || 
                        (navigator as any).mozConnection || 
                        (navigator as any).webkitConnection;
      
      const status: NetworkStatus = {
        isOnline: navigator.onLine,
        connectionType: connection?.type,
        effectiveType: connection?.effectiveType,
        downlink: connection?.downlink,
        rtt: connection?.rtt
      };
      
      setNetworkStatus(status);
      onNetworkChange?.(status);
      
      console.log('🌐 网络状态更新:', status);
    };

    const handleOnline = () => {
      console.log('✅ 网络连接已恢复');
      updateNetworkStatus();
    };

    const handleOffline = () => {
      console.log('❌ 网络连接已断开');
      updateNetworkStatus();
      addNetworkError({
        type: 'unknown',
        message: '网络连接已断开',
        timestamp: Date.now()
      });
    };

    // 监听网络状态变化
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // 监听连接信息变化
    const connection = (navigator as any).connection;
    if (connection) {
      connection.addEventListener('change', updateNetworkStatus);
    }

    // 初始化网络状态
    updateNetworkStatus();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      if (connection) {
        connection.removeEventListener('change', updateNetworkStatus);
      }
    };
  }, [onNetworkChange]);

  // 监控网络错误
  useEffect(() => {
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args);
        return response;
      } catch (error) {
        const url = typeof args[0] === 'string' ? args[0] : args[0].url;
        const domain = new URL(url).hostname;
        
        let errorType: NetworkError['type'] = 'unknown';
        let errorMessage = error instanceof Error ? error.message : '未知网络错误';
        
        if (errorMessage.includes('ERR_CONNECTION_RESET')) {
          errorType = 'connection_reset';
          errorMessage = '连接被重置，可能是网络不稳定或服务器问题';
        } else if (errorMessage.includes('ERR_NAME_NOT_RESOLVED')) {
          errorType = 'dns_failure';
          errorMessage = 'DNS解析失败，请检查网络设置';
        } else if (errorMessage.includes('timeout')) {
          errorType = 'timeout';
          errorMessage = '请求超时，网络连接可能较慢';
        }
        
        addNetworkError({
          type: errorType,
          domain,
          message: errorMessage,
          timestamp: Date.now()
        });
        
        throw error;
      }
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, []);

  const addNetworkError = (error: NetworkError) => {
    setNetworkErrors(prev => {
      const newErrors = [error, ...prev].slice(0, 10); // 只保留最近10个错误
      return newErrors;
    });
  };

  const getNetworkStatusIcon = () => {
    if (!networkStatus.isOnline) {
      return <DisconnectOutlined style={{ color: '#ff4d4f' }} />;
    }
    
    if (networkStatus.rtt && networkStatus.rtt > 500) {
      return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
    }
    
    return <WifiOutlined style={{ color: '#52c41a' }} />;
  };

  const getNetworkStatusText = () => {
    if (!networkStatus.isOnline) {
      return '离线';
    }
    
    if (networkStatus.effectiveType) {
      return `在线 (${networkStatus.effectiveType})`;
    }
    
    return '在线';
  };

  const getConnectionQuality = () => {
    if (!networkStatus.isOnline) return 'offline';
    if (!networkStatus.rtt) return 'unknown';
    
    if (networkStatus.rtt < 100) return 'excellent';
    if (networkStatus.rtt < 300) return 'good';
    if (networkStatus.rtt < 500) return 'fair';
    return 'poor';
  };

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return '#52c41a';
      case 'good': return '#1890ff';
      case 'fair': return '#faad14';
      case 'poor': return '#ff4d4f';
      case 'offline': return '#d9d9d9';
      default: return '#d9d9d9';
    }
  };

  const handleRetry = () => {
    window.location.reload();
  };

  const recentErrors = networkErrors.slice(0, 3);
  const hasRecentErrors = recentErrors.length > 0;

  return (
    <>
      {/* 网络状态指示器 */}
      <div 
        style={{ 
          position: 'fixed', 
          top: 16, 
          right: 16, 
          zIndex: 1000,
          cursor: showDetailedInfo ? 'pointer' : 'default'
        }}
        onClick={() => showDetailedInfo && setShowStatusDetails(true)}
      >
        <Space>
          {getNetworkStatusIcon()}
          <Text style={{ fontSize: '12px' }}>
            {getNetworkStatusText()}
          </Text>
          {hasRecentErrors && (
            <Tag 
              color="red" 
              size="small"
              style={{ cursor: 'pointer' }}
              onClick={(e) => {
                e.stopPropagation();
                setShowErrorModal(true);
              }}
            >
              {networkErrors.length} 错误
            </Tag>
          )}
        </Space>
      </div>

      {/* 网络离线警告 */}
      {!networkStatus.isOnline && (
        <Alert
          message="网络连接已断开"
          description="请检查您的网络连接，某些功能可能无法正常使用。"
          type="error"
          showIcon
          action={
            <Button size="small" onClick={handleRetry}>
              <ReloadOutlined /> 重试
            </Button>
          }
          style={{
            position: 'fixed',
            top: 60,
            right: 16,
            width: 320,
            zIndex: 1000
          }}
        />
      )}

      {/* 网络错误详情模态框 */}
      <Modal
        title="网络错误详情"
        open={showErrorModal}
        onCancel={() => setShowErrorModal(false)}
        footer={[
          <Button key="close" onClick={() => setShowErrorModal(false)}>
            关闭
          </Button>,
          <Button key="retry" type="primary" onClick={handleRetry}>
            <ReloadOutlined /> 刷新页面
          </Button>
        ]}
        width={600}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {networkErrors.length === 0 ? (
            <Text>暂无网络错误记录</Text>
          ) : (
            networkErrors.map((error, index) => (
              <div key={index} style={{ padding: '8px', border: '1px solid #f0f0f0', borderRadius: '4px' }}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Space>
                    <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                    <Text strong>{error.type.replace('_', ' ').toUpperCase()}</Text>
                    {error.domain && <Tag>{error.domain}</Tag>}
                  </Space>
                  <Text>{error.message}</Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {new Date(error.timestamp).toLocaleString()}
                  </Text>
                </Space>
              </div>
            ))
          )}
        </Space>
      </Modal>

      {/* 网络状态详情模态框 */}
      <Modal
        title="网络状态详情"
        open={showStatusDetails}
        onCancel={() => setShowStatusDetails(false)}
        footer={[
          <Button key="close" onClick={() => setShowStatusDetails(false)}>
            关闭
          </Button>
        ]}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>连接状态: </Text>
            <Space>
              {networkStatus.isOnline ? (
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
              ) : (
                <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
              )}
              <Text>{networkStatus.isOnline ? '在线' : '离线'}</Text>
            </Space>
          </div>
          
          {networkStatus.connectionType && (
            <div>
              <Text strong>连接类型: </Text>
              <Text>{networkStatus.connectionType}</Text>
            </div>
          )}
          
          {networkStatus.effectiveType && (
            <div>
              <Text strong>网络速度: </Text>
              <Tag color={getQualityColor(getConnectionQuality())}>
                {networkStatus.effectiveType}
              </Tag>
            </div>
          )}
          
          {networkStatus.downlink && (
            <div>
              <Text strong>下行带宽: </Text>
              <Text>{networkStatus.downlink} Mbps</Text>
            </div>
          )}
          
          {networkStatus.rtt && (
            <div>
              <Text strong>网络延迟: </Text>
              <Text>{networkStatus.rtt} ms</Text>
            </div>
          )}
          
          <Divider />
          
          <div>
            <Text strong>连接质量: </Text>
            <Tag color={getQualityColor(getConnectionQuality())}>
              {getConnectionQuality().toUpperCase()}
            </Tag>
          </div>
        </Space>
      </Modal>
    </>
  );
};

export default NetworkStatusMonitor;
