import { useState, useEffect, useCallback } from 'react';
import { App } from 'antd';
import { characterAPI } from '../services/characterAPI';
import { useAgentStore } from '../store/agent';
import { useSessionStore } from '../store/session';
import { VRMStorageInfo } from '../types/vrm';
import { GenderEnum } from '../types/agent';
import { handleError } from '../utils/errorHandler';

interface Character {
  id: string;
  name: string;
  description: string;
  vrmModelUrl?: string;
  greeting?: string;
  settings?: {
    voice_type?: string;
    animation_style?: string;
  };
  vrm_model_config?: {
    voice_type?: string;
    animation_style?: string;
    greeting?: string;
    systemRole?: string;
  };
  system_role?: string;
  image_url?: string;
  cover?: string;
  avatar?: string;
  gender?: string;
  category?: string;
}

interface UseCharacterLoaderOptions {
  onCharacterLoaded?: (character: Character) => void;
  onVRMModelSet?: (model: VRMStorageInfo) => void;
  onBackgroundSet?: (url: string, type: 'character' | 'gradient') => void;
}

interface UseCharacterLoaderReturn {
  selectedCharacter: Character | null;
  selectedVrmModel: VRMStorageInfo | null;
  loading: boolean;
  error: string | null;
  loadCharacter: (characterId: string) => Promise<void>;
  resetCharacter: () => void;
}

/**
 * 角色数据加载Hook
 * 支持从API和localStorage加载角色数据，处理VRM模型和Agent数据转换
 */
export const useCharacterLoader = (options: UseCharacterLoaderOptions = {}): UseCharacterLoaderReturn => {
  const { onCharacterLoaded, onVRMModelSet, onBackgroundSet } = options;
  const { message } = App.useApp();
  const { addLocalAgent, getAgentById } = useAgentStore();
  const { createSession } = useSessionStore();

  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null);
  const [selectedVrmModel, setSelectedVrmModel] = useState<VRMStorageInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);



  /**
   * 从VRM模型创建角色数据
   */
  const createCharacterFromVRM = useCallback(async (vrmModel: VRMStorageInfo): Promise<boolean> => {
    try {
      const characterId = getCharacterIdForVrmModel(vrmModel.id);
      
      const virtualCharacter: Character = {
        id: characterId,
        name: vrmModel.name,
        description: vrmModel.metadata?.description || `与${vrmModel.name}的沉浸式对话`,
        vrmModelUrl: vrmModel.vrmModelUrl,
        settings: {
          voice_type: vrmModel.config?.voice_type || 'zh-CN-XiaoxiaoNeural',
          animation_style: vrmModel.config?.animation_style || 'default'
        }
      };

      setSelectedCharacter(virtualCharacter);
      setSelectedVrmModel(vrmModel);

      // 创建Agent数据
      const agentData = createAgentFromVRM(vrmModel);
      addLocalAgent(agentData);
      createSession(agentData);

      onCharacterLoaded?.(virtualCharacter);
      onVRMModelSet?.(vrmModel);

      console.log('VRM模型角色创建成功');
      return true;
    } catch (error) {
      console.error('从VRM模型创建角色失败:', error);
      return false;
    }
  }, [addLocalAgent, createSession, onCharacterLoaded, onVRMModelSet]);

  /**
   * 从API加载角色数据
   */
  const loadFromAPI = useCallback(async (characterId: string): Promise<boolean> => {
    try {
      console.log('从API加载角色:', characterId);
      const response = await characterAPI.getCharacterDetail(characterId);
      const characterData = response.data;

      if (!characterData || !characterData.id) {
        console.error('角色数据无效:', characterData);
        setError('角色数据无效或不存在');
        return false;
      }

      setSelectedCharacter(characterData);

      // 创建Agent数据
      const agentData = createAgentFromCharacter(characterData);
      const existingAgent = getAgentById(characterData.id);
      if (!existingAgent) {
        addLocalAgent(agentData);
      }
      createSession(agentData);

      // 如果角色有VRM模型URL，创建VRM模型信息
      if (characterData.vrmModelUrl) {
        const vrmModelInfo = createVRMInfoFromCharacter(characterData);
        setSelectedVrmModel(vrmModelInfo);
        onVRMModelSet?.(vrmModelInfo);
      }

      // 加载角色背景图片
      await loadCharacterBackground(characterId);

      onCharacterLoaded?.(characterData);
      console.log('API角色加载成功');
      return true;
    } catch (error) {
      console.error('从API加载角色失败:', error);
      handleError(error, 'useCharacterLoader - 加载角色');
      setError('加载角色失败');
      return false;
    }
  }, [addLocalAgent, getAgentById, createSession, onCharacterLoaded, onVRMModelSet, onBackgroundSet]);

  /**
   * 加载角色背景图片
   */
  const loadCharacterBackground = useCallback(async (characterId: string) => {
    try {
      const backgroundsResponse = await characterAPI.getCharacterBackgrounds(characterId);
      const backgrounds = backgroundsResponse.data;
      
      if (backgrounds && backgrounds.length > 0) {
        const randomIndex = Math.floor(Math.random() * backgrounds.length);
        const selectedBackground = backgrounds[randomIndex];
        onBackgroundSet?.(selectedBackground.image_url, 'character');
      } else {
        onBackgroundSet?.('', 'gradient');
      }
    } catch (bgError) {
      console.warn('加载背景图片失败:', bgError);
      onBackgroundSet?.('', 'gradient');
    }
  }, [onBackgroundSet]);

  /**
   * 主加载函数
   */
  const loadCharacter = useCallback(async (characterId?: string) => {
    setLoading(true);
    setError(null);

    try {
      // 内联loadFromVRMModel逻辑，避免依赖循环
      const vrmLoaded = await (async (characterId?: string): Promise<boolean> => {
        if (!characterId) {
          const selectedVrmModel = localStorage.getItem('selectedVrmModel');
          if (selectedVrmModel) {
            try {
              const vrmModel: VRMStorageInfo = JSON.parse(selectedVrmModel);
              console.log('从localStorage加载VRM模型:', vrmModel);
              return await createCharacterFromVRM(vrmModel);
            } catch (error) {
              console.error('解析VRM模型数据失败:', error);
              localStorage.removeItem('selectedVrmModel');
            }
          }
          return false;
        }

        const isVrmModelId = isNaN(parseInt(characterId));
        if (!isVrmModelId) return false;

        console.log('检测到VRM模型ID:', characterId);
        const storedVrmModels = localStorage.getItem('vrm_downloaded_models');

        if (storedVrmModels) {
          try {
            const vrmModels: VRMStorageInfo[] = JSON.parse(storedVrmModels);
            const vrmModel = vrmModels.find(model =>
              model.id === characterId ||
              model.name.replace(/\s+/g, '-').toLowerCase() === characterId
            );

            if (vrmModel) {
              console.log('找到匹配的VRM模型:', vrmModel);
              return await createCharacterFromVRM(vrmModel);
            }
          } catch (error) {
            console.error('解析VRM模型数据失败:', error);
          }
        }

        return false;
      })(characterId);

      if (vrmLoaded) {
        setLoading(false);
        return;
      }

      // 如果没有角色ID且VRM加载失败，显示错误
      if (!characterId) {
        setError('未指定角色ID，且没有找到可用的VRM模型');
        setLoading(false);
        return;
      }

      // 内联loadFromAPI逻辑
      const apiLoaded = await loadFromAPI(characterId);
      if (!apiLoaded) {
        setError(`未找到角色: ${characterId}`);
      }
    } catch (error) {
      console.error('加载角色过程出错:', error);
      setError('加载角色时发生错误');
    } finally {
      setLoading(false);
    }
  }, [createCharacterFromVRM, loadFromAPI]); // 只保留必要的依赖

  /**
   * 重置角色数据
   */
  const resetCharacter = useCallback(() => {
    setSelectedCharacter(null);
    setSelectedVrmModel(null);
    setLoading(true);
    setError(null);
  }, []);

  return {
    selectedCharacter,
    selectedVrmModel,
    loading,
    error,
    loadCharacter,
    resetCharacter,
  };
};

// 辅助函数
const getCharacterIdForVrmModel = (vrmId: string): string => {
  return vrmId; // 简化处理，直接使用VRM ID
};

const createAgentFromVRM = (vrmModel: VRMStorageInfo) => {
  return {
    agentId: vrmModel.id,
    meta: {
      name: vrmModel.name,
      description: vrmModel.metadata?.description || `与${vrmModel.name}的沉浸式对话`,
      avatar: vrmModel.metadata?.avatar || '',
      model: vrmModel.vrmModelUrl,
      readme: vrmModel.metadata?.description || '',
      tags: vrmModel.metadata?.tags || [],
      cover: vrmModel.metadata?.cover || '',
      gender: GenderEnum.FEMALE,
    },
    systemRole: `你是${vrmModel.name}，一个友好的虚拟角色。`,
    chatConfig: {
      historyCount: 10,
      compressThreshold: 1000,
      enableCompressThreshold: true,
      enableHistoryCount: true,
    },
    tts: {
      voice: vrmModel.config?.voice_type || 'zh-CN-XiaoxiaoNeural',
      speed: 1,
      pitch: 0,
    },
  };
};

const createAgentFromCharacter = (characterData: Character) => {
  return {
    agentId: characterData.id,
    meta: {
      name: characterData.name,
      description: characterData.description,
      avatar: characterData.avatar || '',
      model: characterData.vrmModelUrl || '',
      readme: characterData.description,
      tags: [],
      cover: characterData.cover || '',
      gender: (characterData.gender as GenderEnum) || GenderEnum.FEMALE,
    },
    systemRole: `你是${characterData.name}，${characterData.description}`,
    chatConfig: {
      historyCount: 10,
      compressThreshold: 1000,
      enableCompressThreshold: true,
      enableHistoryCount: true,
    },
    tts: {
      engine: 'edge' as const,
      locale: 'zh-CN',
      voice: characterData.settings?.voice_type || 'zh-CN-XiaoxiaoNeural',
      speed: 1,
      pitch: 1,
      message: '',
    },
    params: {},
    provider: 'openai',
    model: 'gpt-3.5-turbo',
  };
};

const createVRMInfoFromCharacter = (characterData: Character): VRMStorageInfo => {
  return {
    id: characterData.id,
    name: characterData.name,
    vrmModelUrl: characterData.vrmModelUrl!,
    downloadedAt: new Date().toISOString(),
    fileSize: '未知',
    config: {
      voice_type: characterData.vrm_model_config?.voice_type || 'zh-CN-XiaoxiaoNeural',
      animation_style: characterData.vrm_model_config?.animation_style || 'default',
      greeting: characterData.vrm_model_config?.greeting || characterData.greeting || `你好，我是${characterData.name}`,
      systemRole: characterData.vrm_model_config?.systemRole || characterData.system_role || `你是${characterData.name}`
    },
    metadata: {
      id: characterData.id,
      name: characterData.name,
      description: characterData.description || `与${characterData.name}的沉浸式对话`,
      vrmModelUrl: characterData.vrmModelUrl!,
      avatar: characterData.image_url || '',
      cover: characterData.cover || '',
      category: characterData.category || 'Character',
      source: 'backend-api'
    }
  };
};
