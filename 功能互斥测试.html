<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能互斥机制测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        .interaction-area {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .voice-area, .touch-area {
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .voice-area {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }
        .touch-area {
            background-color: #f3e5f5;
            border-color: #9c27b0;
        }
        .voice-area.active {
            background-color: #1976d2;
            color: white;
            box-shadow: 0 4px 15px rgba(25, 118, 210, 0.4);
        }
        .touch-area.active {
            background-color: #7b1fa2;
            color: white;
            box-shadow: 0 4px 15px rgba(123, 31, 162, 0.4);
        }
        .voice-area.disabled, .touch-area.disabled {
            background-color: #f5f5f5;
            color: #999;
            border-color: #ccc;
            cursor: not-allowed;
            opacity: 0.6;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        button:hover:not(:disabled) {
            background-color: #0056b3;
            transform: translateY(-1px);
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .status-panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-label {
            font-weight: bold;
            color: #555;
        }
        .status-value {
            color: #007bff;
            font-family: monospace;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .log-entry.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .log-entry.warn {
            background-color: #fff3cd;
            color: #856404;
        }
        .log-entry.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log-entry.success {
            background-color: #d4edda;
            color: #155724;
        }
        .test-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        .ai-speaking-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #ff5722;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-weight: bold;
            display: none;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .conflict-indicator {
            background-color: #f44336;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
            animation: shake 0.5s ease-in-out;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 功能互斥机制测试</h1>
        
        <div class="ai-speaking-indicator" id="aiSpeakingIndicator">
            🤖 AI正在说话...
        </div>
        
        <div class="conflict-indicator" id="conflictIndicator">
            ⚠️ 交互冲突检测到！
        </div>
        
        <div class="test-section">
            <h3>📋 测试说明</h3>
            <p>本页面用于测试语音交互和触摸交互的互斥机制，验证两个功能不会同时运行造成冲突。</p>
            <div class="status-panel">
                <div class="status-item">
                    <span class="status-label">测试目标：</span>
                    <span class="status-value">验证功能互斥机制</span>
                </div>
                <div class="status-item">
                    <span class="status-label">语音优先策略：</span>
                    <span class="status-value">启用</span>
                </div>
                <div class="status-item">
                    <span class="status-label">触摸事件拦截：</span>
                    <span class="status-value">启用</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎮 交互测试区域</h3>
            <div class="interaction-area">
                <div class="voice-area" id="voiceArea">
                    <h4>🎤 语音交互</h4>
                    <p>点击开始语音交互</p>
                    <button onclick="startVoiceInteraction()">开始语音</button>
                    <button onclick="endVoiceInteraction()">结束语音</button>
                </div>
                
                <div class="touch-area" id="touchArea">
                    <h4>👆 触摸交互</h4>
                    <p>点击开始触摸交互</p>
                    <button onclick="startTouchInteraction()">开始触摸</button>
                    <button onclick="endTouchInteraction()">结束触摸</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 交互状态监控</h3>
            <div class="status-panel" id="statusPanel">
                <div class="status-item">
                    <span class="status-label">当前模式：</span>
                    <span class="status-value" id="currentMode">idle</span>
                </div>
                <div class="status-item">
                    <span class="status-label">语音状态：</span>
                    <span class="status-value" id="voiceStatus">未激活</span>
                </div>
                <div class="status-item">
                    <span class="status-label">触摸状态：</span>
                    <span class="status-value" id="touchStatus">未激活</span>
                </div>
                <div class="status-item">
                    <span class="status-label">AI说话状态：</span>
                    <span class="status-value" id="aiSpeakingStatus">未说话</span>
                </div>
                <div class="status-item">
                    <span class="status-label">优先级：</span>
                    <span class="status-value" id="priority">无</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📈 测试统计</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalInteractions">0</div>
                    <div class="stat-label">总交互次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="voiceInteractions">0</div>
                    <div class="stat-label">语音交互次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="touchInteractions">0</div>
                    <div class="stat-label">触摸交互次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="conflictCount">0</div>
                    <div class="stat-label">冲突次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="successRate">100%</div>
                    <div class="stat-label">成功率</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 自动化测试</h3>
            <div class="test-controls">
                <button onclick="runBasicTest()">基础功能测试</button>
                <button onclick="runConflictTest()">冲突场景测试</button>
                <button onclick="runStressTest()">压力测试</button>
                <button onclick="runTimeoutTest()">超时测试</button>
                <button onclick="simulateAISpeaking()">模拟AI说话</button>
                <button onclick="resetAllStates()">重置所有状态</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 实时日志</h3>
            <div class="log" id="logOutput">等待测试开始...</div>
            <div class="test-controls">
                <button onclick="clearLog()">清除日志</button>
                <button onclick="exportTestResults()">导出测试结果</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟交互管理器状态
        let interactionState = {
            mode: 'idle',
            isVoiceActive: false,
            isTouchActive: false,
            isAISpeaking: false,
            priority: null,
            lastInteractionTime: 0,
            interactionCount: 0
        };

        // 统计数据
        let stats = {
            totalInteractions: 0,
            voiceInteractions: 0,
            touchInteractions: 0,
            conflictCount: 0,
            successfulInteractions: 0
        };

        // 测试配置
        const config = {
            voicePriority: true,
            touchTimeout: 5000,
            voiceTimeout: 30000,
            enableLogging: true
        };

        // 超时定时器
        let interactionTimeout = null;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 功能互斥测试页面已加载', 'success');
            log('💡 点击交互区域开始测试', 'info');
            updateUI();
        });

        // 记录日志
        function log(message, level = 'info') {
            const logEl = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;
        }

        // 更新UI状态
        function updateUI() {
            // 更新状态显示
            document.getElementById('currentMode').textContent = interactionState.mode;
            document.getElementById('voiceStatus').textContent = interactionState.isVoiceActive ? '激活' : '未激活';
            document.getElementById('touchStatus').textContent = interactionState.isTouchActive ? '激活' : '未激活';
            document.getElementById('aiSpeakingStatus').textContent = interactionState.isAISpeaking ? '正在说话' : '未说话';
            document.getElementById('priority').textContent = interactionState.priority || '无';

            // 更新统计数据
            document.getElementById('totalInteractions').textContent = stats.totalInteractions;
            document.getElementById('voiceInteractions').textContent = stats.voiceInteractions;
            document.getElementById('touchInteractions').textContent = stats.touchInteractions;
            document.getElementById('conflictCount').textContent = stats.conflictCount;
            
            const successRate = stats.totalInteractions > 0 
                ? Math.round((stats.successfulInteractions / stats.totalInteractions) * 100)
                : 100;
            document.getElementById('successRate').textContent = successRate + '%';

            // 更新交互区域样式
            const voiceArea = document.getElementById('voiceArea');
            const touchArea = document.getElementById('touchArea');

            // 重置样式
            voiceArea.className = 'voice-area';
            touchArea.className = 'touch-area';

            // 应用当前状态样式
            if (interactionState.isVoiceActive) {
                voiceArea.classList.add('active');
                touchArea.classList.add('disabled');
            } else if (interactionState.isTouchActive) {
                touchArea.classList.add('active');
                voiceArea.classList.add('disabled');
            } else if (interactionState.isAISpeaking) {
                voiceArea.classList.add('disabled');
                touchArea.classList.add('disabled');
            }

            // AI说话指示器
            const aiIndicator = document.getElementById('aiSpeakingIndicator');
            aiIndicator.style.display = interactionState.isAISpeaking ? 'block' : 'none';
        }

        // 显示冲突指示器
        function showConflict() {
            const conflictIndicator = document.getElementById('conflictIndicator');
            conflictIndicator.style.display = 'block';
            setTimeout(() => {
                conflictIndicator.style.display = 'none';
            }, 3000);
        }

        // 清除超时定时器
        function clearInteractionTimeout() {
            if (interactionTimeout) {
                clearTimeout(interactionTimeout);
                interactionTimeout = null;
            }
        }

        // 设置交互超时
        function setInteractionTimeout(mode, duration) {
            clearInteractionTimeout();
            interactionTimeout = setTimeout(() => {
                log(`${mode} 交互超时，自动重置`, 'warn');
                resetToIdle();
            }, duration);
        }

        // 重置到空闲状态
        function resetToIdle() {
            clearInteractionTimeout();
            interactionState.mode = 'idle';
            interactionState.isVoiceActive = false;
            interactionState.isTouchActive = false;
            interactionState.priority = null;
            log('交互状态重置为idle', 'info');
            updateUI();
        }

        // 开始语音交互
        function startVoiceInteraction() {
            log('尝试开始语音交互', 'info');

            if (interactionState.isVoiceActive) {
                log('语音交互已经激活，忽略重复请求', 'warn');
                return;
            }

            if (interactionState.isTouchActive) {
                if (config.voicePriority) {
                    log('语音优先，中断触摸交互', 'warn');
                    stats.conflictCount++;
                    showConflict();
                } else {
                    log('触摸交互进行中，语音请求被拒绝', 'error');
                    stats.conflictCount++;
                    showConflict();
                    updateUI();
                    return;
                }
            }

            if (interactionState.isAISpeaking) {
                log('AI正在说话，语音请求被拒绝', 'error');
                stats.conflictCount++;
                showConflict();
                updateUI();
                return;
            }

            // 开始语音交互
            interactionState.mode = 'voice';
            interactionState.isVoiceActive = true;
            interactionState.isTouchActive = false;
            interactionState.priority = 'voice';
            interactionState.lastInteractionTime = Date.now();
            interactionState.interactionCount++;

            stats.totalInteractions++;
            stats.voiceInteractions++;
            stats.successfulInteractions++;

            setInteractionTimeout('voice', config.voiceTimeout);

            log('语音交互已开始', 'success');
            updateUI();
        }

        // 结束语音交互
        function endVoiceInteraction() {
            if (!interactionState.isVoiceActive) {
                log('语音交互未激活，忽略结束请求', 'warn');
                return;
            }

            clearInteractionTimeout();
            interactionState.mode = 'idle';
            interactionState.isVoiceActive = false;
            interactionState.priority = null;

            log('语音交互已结束', 'success');
            updateUI();
        }

        // 开始触摸交互
        function startTouchInteraction() {
            log('尝试开始触摸交互', 'info');

            if (interactionState.isTouchActive) {
                log('触摸交互已经激活，忽略重复请求', 'warn');
                return;
            }

            if (interactionState.isVoiceActive) {
                log('语音交互进行中，触摸请求被拒绝', 'error');
                stats.conflictCount++;
                showConflict();
                updateUI();
                return;
            }

            if (interactionState.isAISpeaking) {
                log('AI正在说话，触摸请求被拒绝', 'error');
                stats.conflictCount++;
                showConflict();
                updateUI();
                return;
            }

            // 开始触摸交互
            interactionState.mode = 'touch';
            interactionState.isTouchActive = true;
            interactionState.priority = 'touch';
            interactionState.lastInteractionTime = Date.now();
            interactionState.interactionCount++;

            stats.totalInteractions++;
            stats.touchInteractions++;
            stats.successfulInteractions++;

            setInteractionTimeout('touch', config.touchTimeout);

            log('触摸交互已开始', 'success');
            updateUI();
        }

        // 结束触摸交互
        function endTouchInteraction() {
            if (!interactionState.isTouchActive) {
                log('触摸交互未激活，忽略结束请求', 'warn');
                return;
            }

            clearInteractionTimeout();
            interactionState.mode = 'idle';
            interactionState.isTouchActive = false;
            interactionState.priority = null;

            log('触摸交互已结束', 'success');
            updateUI();
        }

        // 模拟AI说话
        function simulateAISpeaking() {
            if (interactionState.isAISpeaking) {
                // 停止AI说话
                interactionState.isAISpeaking = false;
                log('AI说话已停止', 'info');
            } else {
                // 开始AI说话
                interactionState.isAISpeaking = true;
                log('AI开始说话', 'info');
                
                // 3秒后自动停止
                setTimeout(() => {
                    if (interactionState.isAISpeaking) {
                        interactionState.isAISpeaking = false;
                        log('AI说话自动结束', 'info');
                        updateUI();
                    }
                }, 3000);
            }
            updateUI();
        }

        // 基础功能测试
        async function runBasicTest() {
            log('🧪 开始基础功能测试', 'info');
            
            // 测试语音交互
            startVoiceInteraction();
            await sleep(1000);
            endVoiceInteraction();
            await sleep(500);
            
            // 测试触摸交互
            startTouchInteraction();
            await sleep(1000);
            endTouchInteraction();
            await sleep(500);
            
            log('✅ 基础功能测试完成', 'success');
        }

        // 冲突场景测试
        async function runConflictTest() {
            log('🧪 开始冲突场景测试', 'info');
            
            // 场景1：语音中启动触摸
            startVoiceInteraction();
            await sleep(500);
            startTouchInteraction(); // 应该被拒绝
            await sleep(500);
            endVoiceInteraction();
            await sleep(500);
            
            // 场景2：触摸中启动语音
            startTouchInteraction();
            await sleep(500);
            startVoiceInteraction(); // 应该中断触摸
            await sleep(500);
            endVoiceInteraction();
            await sleep(500);
            
            // 场景3：AI说话时启动交互
            simulateAISpeaking();
            await sleep(500);
            startVoiceInteraction(); // 应该被拒绝
            startTouchInteraction(); // 应该被拒绝
            await sleep(1000);
            simulateAISpeaking(); // 停止AI说话
            
            log('✅ 冲突场景测试完成', 'success');
        }

        // 压力测试
        async function runStressTest() {
            log('🧪 开始压力测试', 'info');
            
            for (let i = 0; i < 10; i++) {
                startVoiceInteraction();
                startTouchInteraction();
                await sleep(100);
                endVoiceInteraction();
                endTouchInteraction();
                await sleep(100);
            }
            
            log('✅ 压力测试完成', 'success');
        }

        // 超时测试
        async function runTimeoutTest() {
            log('🧪 开始超时测试', 'info');
            
            // 设置短超时进行测试
            const originalTimeout = config.voiceTimeout;
            config.voiceTimeout = 2000;
            
            startVoiceInteraction();
            log('等待语音交互超时...', 'info');
            
            // 恢复原始超时
            setTimeout(() => {
                config.voiceTimeout = originalTimeout;
                log('✅ 超时测试完成', 'success');
            }, 3000);
        }

        // 重置所有状态
        function resetAllStates() {
            clearInteractionTimeout();
            interactionState = {
                mode: 'idle',
                isVoiceActive: false,
                isTouchActive: false,
                isAISpeaking: false,
                priority: null,
                lastInteractionTime: 0,
                interactionCount: 0
            };
            
            stats = {
                totalInteractions: 0,
                voiceInteractions: 0,
                touchInteractions: 0,
                conflictCount: 0,
                successfulInteractions: 0
            };
            
            log('🔄 所有状态已重置', 'info');
            updateUI();
        }

        // 清除日志
        function clearLog() {
            document.getElementById('logOutput').innerHTML = '';
        }

        // 导出测试结果
        function exportTestResults() {
            const results = {
                timestamp: new Date().toISOString(),
                stats: stats,
                interactionState: interactionState,
                config: config
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `interaction_test_results_${Date.now()}.json`;
            a.click();
            window.URL.revokeObjectURL(url);
            
            log('📊 测试结果已导出', 'success');
        }

        // 工具函数：延迟
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    </script>
</body>
</html>
