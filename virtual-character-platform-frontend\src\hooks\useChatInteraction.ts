import { useState, useCallback } from 'react';
import { characterAPI } from '../services/characterAPI';
import { analyzeEmotion, handleSpeakAi } from '../services/chat';
import { useSessionStore } from '../store/session';
import { useInteractionManager } from './useInteractionManager';
import { handleError } from '../utils/errorHandler';
import { useGlobalStore } from '../store/global';

interface Character {
  id: string;
  name: string;
}

interface UseChatInteractionOptions {
  character: Character | null;
  interactionManager?: any; // 传入外部的interactionManager
  onEmotionChange?: (emotion: string) => void;
  onAISpeakingChange?: (speaking: boolean) => void;
  onLastResponseChange?: (response: string) => void;
  onChatBoxModeChange?: (mode: 'minimal' | 'expanded' | 'hidden') => void;
}

interface UseChatInteractionReturn {
  isProcessing: boolean;
  isAISpeaking: boolean;
  lastAIResponse: string;
  handleVoiceInput: (transcript: string) => Promise<void>;
  handleTextMessage: (content: string) => Promise<void>;
  playAudioFromUrl: (audioUrl: string) => Promise<void>;
}

/**
 * 聊天交互处理Hook
 * 处理语音输入、文字消息、AI回复等聊天相关功能
 */
export const useChatInteraction = (options: UseChatInteractionOptions): UseChatInteractionReturn => {
  const {
    character,
    interactionManager: externalInteractionManager,
    onEmotionChange,
    onAISpeakingChange,
    onLastResponseChange,
    onChatBoxModeChange
  } = options;

  const [isProcessing, setIsProcessing] = useState(false);
  const [isAISpeaking, setIsAISpeaking] = useState(false);
  const [lastAIResponse, setLastAIResponse] = useState<string>('');

  const { dispatchMessage } = useSessionStore();
  const defaultInteractionManager = useInteractionManager();
  const interactionManager = externalInteractionManager || defaultInteractionManager;

  /**
   * 映射角色ID到数字ID
   */
  const mapCharacterIdToNumeric = useCallback((characterId: string): string => {
    const idMapping: Record<string, string> = {
      'klee-sample': '27',
      'ayaka-sample': '28',
      'hutao-sample': '3'
    };
    return idMapping[characterId] || '28';
  }, []);

  /**
   * 播放音频URL
   */
  const playAudioFromUrl = useCallback(async (audioUrl: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      console.log('🔊 playAudioFromUrl: 开始播放音频URL:', audioUrl);
      console.log('🔊 playAudioFromUrl: 当前交互状态:', {
        isVoiceActive: interactionManager.isVoiceActive,
        isAISpeaking: interactionManager.isAISpeaking,
        currentMode: interactionManager.currentMode
      });

      const audio = new Audio(audioUrl);

      audio.onloadeddata = () => {
        console.log('🔊 playAudioFromUrl: 音频数据加载完成');
      };

      audio.oncanplay = () => {
        console.log('🔊 playAudioFromUrl: 音频可以播放，开始播放');
        audio.play().then(() => {
          console.log('🔊 playAudioFromUrl: 音频开始播放成功');
        }).catch((playError) => {
          console.error('🔊 playAudioFromUrl: 音频播放启动失败:', playError);
          reject(playError);
        });
      };

      audio.onended = () => {
        console.log('🔊 playAudioFromUrl: 音频播放完成');
        resolve();
      };

      audio.onerror = (error) => {
        console.error('🔊 playAudioFromUrl: 音频播放错误:', error);
        reject(new Error('音频播放失败'));
      };

      audio.src = audioUrl;
      audio.load();
    });
  }, [interactionManager]);

  /**
   * 处理语音输入
   */
  const handleVoiceInput = useCallback(async (transcript: string) => {
    console.log('🎤 handleVoiceInput 被调用，transcript:', transcript);
    console.log('🎤 当前状态 - character:', character?.name, 'isProcessing:', isProcessing);

    if (!transcript.trim() || !character || isProcessing) {
      console.log('🎤 语音输入被跳过 - transcript为空或角色未加载或正在处理中');
      return;
    }

    // 检查是否可以进行语音交互
    console.log('🎤 检查交互管理器状态...');
    if (!interactionManager.canInteract('voice')) {
      console.warn('🎤 语音交互被阻止，当前状态:', interactionManager.currentMode);
      return;
    }

    // 开始语音交互
    console.log('🎤 尝试启动语音交互...');
    const result = interactionManager.startVoiceInteraction();
    if (!result.success) {
      console.warn('🎤 语音交互启动失败:', result.reason);
      return;
    }

    console.log('🎤 语音交互启动成功，开始处理语音输入:', transcript);

    setIsProcessing(true);
    onEmotionChange?.('listening');

    // 添加用户消息
    const userMessageId = Date.now().toString();
    dispatchMessage({
      payload: {
        content: transcript,
        id: userMessageId,
        role: 'user',
      },
      type: 'ADD_MESSAGE',
    });

    // 添加加载中的AI消息
    const aiMessageId = (Date.now() + 1).toString();
    dispatchMessage({
      payload: {
        content: '...',
        id: aiMessageId,
        parentId: userMessageId,
        role: 'assistant',
      },
      type: 'ADD_MESSAGE',
    });

    // 设置加载状态
    useSessionStore.setState({ chatLoadingId: aiMessageId });

    try {
      // 1. 情感分析
      console.log('🧠 开始情感分析用户输入...');
      const userEmotionAnalysis = await analyzeEmotion(transcript);
      console.log('用户情感分析结果:', userEmotionAnalysis);

      // 2. 映射角色ID
      const numericId = mapCharacterIdToNumeric(character.id);
      console.log(`🔄 ID映射: ${character.id} → ${numericId}`);

      // 3. 调用AI服务
      const response = await characterAPI.sendMessage({
        characterId: numericId,
        message: transcript,
        enable_tts: true,
        voice_mode: true
      });

      console.log('AI响应:', response);

      if ((response as any).response) {
        // 4. 分析AI回复的情感
        console.log('🎭 开始分析AI回复的情感...');
        const aiEmotionAnalysis = await analyzeEmotion((response as any).response);
        console.log('AI回复情感分析结果:', aiEmotionAnalysis);

        // 5. 更新AI回复内容
        dispatchMessage({
          payload: {
            content: (response as any).response,
            id: aiMessageId,
          },
          type: 'UPDATE_MESSAGE',
        });

        // 6. 清除加载状态
        useSessionStore.setState({ chatLoadingId: undefined });

        // 7. 设置角色表情
        if (aiEmotionAnalysis.expression) {
          onEmotionChange?.(aiEmotionAnalysis.expression);
        } else {
          onEmotionChange?.('happy');
        }

        // 8. 语音播放 - 先结束语音交互，再开始AI播放
        const responseText = (response as any).response || (response as any).character_response;
        const audioUrl = (response as any).audio_url;

        if (responseText) {
          console.log('🔊 准备播放AI语音回复:', responseText);

          // 先结束语音交互状态，避免状态冲突
          console.log('🔊 结束语音交互状态，准备AI播放');
          interactionManager.endVoiceInteraction();

          // 设置AI播放状态
          console.log('🔊 设置AI播放状态');
          interactionManager.setAISpeaking(true);
          setIsAISpeaking(true);
          onAISpeakingChange?.(true);

          try {
            // 优先使用后端音频，否则使用前端TTS
            if (audioUrl) {
              console.log('🔊 使用后端音频URL:', audioUrl);
              await playAudioFromUrl(audioUrl);
            } else {
              console.log('🔊 使用前端TTS生成语音');
              await handleSpeakAi(responseText);
            }

            console.log('🔊 语音播放完成');
            onEmotionChange?.('neutral');
          } catch (audioError) {
            console.error('🔊 语音播放失败:', audioError);
            // 如果后端音频失败，尝试前端TTS作为备选
            if (audioUrl && responseText) {
              console.log('🔊 后端音频失败，回退到前端TTS');
              try {
                await handleSpeakAi(responseText);
              } catch (ttsError) {
                console.error('🔊 前端TTS也失败:', ttsError);
              }
            }
          } finally {
            console.log('🔊 清理AI播放状态');
            interactionManager.setAISpeaking(false);
            setIsAISpeaking(false);
            onAISpeakingChange?.(false);


          }

          // 记录AI回复内容
          setLastAIResponse(responseText);
          onLastResponseChange?.(responseText);
          console.log('📝 记录AI回复内容:', responseText);
        }
      }

      // 如果聊天框是隐藏的，显示为最小化模式
      onChatBoxModeChange?.('minimal');

    } catch (error) {
      console.error('🚨 语音交互失败:', error);
      handleError(error, 'useChatInteraction - 语音交互');
      onEmotionChange?.('neutral');
      useSessionStore.setState({ chatLoadingId: undefined });

      // 确保清理所有状态
      console.log('🚨 错误处理：清理所有交互状态');
      interactionManager.setAISpeaking(false);
      interactionManager.endVoiceInteraction();
      setIsAISpeaking(false);
      onAISpeakingChange?.(false);
    } finally {
      // 重置处理状态 - 不再延迟，因为语音交互已经在AI播放前结束
      console.log('🎤 清理语音输入处理状态');
      setIsProcessing(false);
      // 注意：不再调用endVoiceInteraction，因为已经在AI播放前调用了
    }
  }, [
    character,
    isProcessing,
    interactionManager,
    dispatchMessage,
    mapCharacterIdToNumeric,
    playAudioFromUrl,
    onEmotionChange,
    onAISpeakingChange,
    onLastResponseChange,
    onChatBoxModeChange
  ]);

  /**
   * 处理文字消息
   */
  const handleTextMessage = useCallback(async (content: string) => {
    if (!content.trim() || !character) {
      return;
    }

    console.log('处理文字消息:', content);
    onEmotionChange?.('thinking');

    // 添加用户消息
    const userMessageId = Date.now().toString();
    dispatchMessage({
      payload: {
        content: content,
        id: userMessageId,
        role: 'user',
      },
      type: 'ADD_MESSAGE',
    });

    // 添加加载中的AI消息
    const aiMessageId = (Date.now() + 1).toString();
    dispatchMessage({
      payload: {
        content: '...',
        id: aiMessageId,
        parentId: userMessageId,
        role: 'assistant',
      },
      type: 'ADD_MESSAGE',
    });

    // 设置加载状态
    useSessionStore.setState({ chatLoadingId: aiMessageId });

    try {
      // 1. 情感分析
      console.log('🧠 开始分析文字输入情感...');
      const userEmotionAnalysis = await analyzeEmotion(content);
      console.log('文字情感分析结果:', userEmotionAnalysis);

      // 2. 映射角色ID
      const numericId = mapCharacterIdToNumeric(character.id);
      console.log(`🔄 ID映射: ${character.id} → ${numericId}`);

      // 3. 调用AI服务
      console.log('发送文字消息到AI后端...');
      const response = await characterAPI.sendMessage({
        characterId: numericId,
        message: content,
        enable_tts: false,
        voice_mode: false
      });

      console.log('AI文字响应:', response);

      if (response && (response as any).response) {
        // 4. 分析AI回复的情感
        console.log('🎭 开始分析AI文字回复的情感...');
        const aiEmotionAnalysis = await analyzeEmotion((response as any).response);
        console.log('AI文字回复情感分析结果:', aiEmotionAnalysis);

        // 5. 设置角色表情
        if (aiEmotionAnalysis.expression) {
          onEmotionChange?.(aiEmotionAnalysis.expression);
        } else {
          onEmotionChange?.('happy');
        }

        // 6. 更新AI回复内容
        dispatchMessage({
          payload: {
            content: (response as any).response,
            id: aiMessageId,
          },
          type: 'UPDATE_MESSAGE',
        });

        // 7. 清除加载状态
        useSessionStore.setState({ chatLoadingId: undefined });

        // 8. 为文字回复添加语音播放
        await handleSpeakAi((response as any).response, {
          onComplete: () => {
            console.log('文字回复语音播放完成');
            onEmotionChange?.('neutral');
          },
          onError: (error) => {
            console.error('文字回复语音播放失败:', error);
          }
        });
      }

    } catch (error) {
      console.error('文字交互失败:', error);
      handleError(error, 'useChatInteraction - 文字交互');
      onEmotionChange?.('neutral');
      useSessionStore.setState({ chatLoadingId: undefined });
    }
  }, [
    character,
    dispatchMessage,
    mapCharacterIdToNumeric,
    onEmotionChange
  ]);

  return {
    isProcessing,
    isAISpeaking,
    lastAIResponse,
    handleVoiceInput,
    handleTextMessage,
    playAudioFromUrl,
  };
};
