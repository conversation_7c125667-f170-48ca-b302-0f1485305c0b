/**
 * VRM模型卡片组件
 * 用于在商城中展示VRM模型，类似VRM测试工具中的展示方式
 */

import React, { useState } from 'react';
import { Card, Button, Tag, Rate, Tooltip, Progress, message } from 'antd';
import { 
  DownloadOutlined, 
  EyeOutlined, 
  HeartOutlined, 
  HeartFilled,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { createStyles } from 'antd-style';
import { VRMModelInfo, VRMDownloadStatus } from '../../types/vrm';
import { VRMModelService } from '../../services/vrmModelService';
import LazyImage from '../LazyImage';
import { processImageUrl } from '../../utils/imageUtils';

const { Meta } = Card;

interface VRMModelCardProps {
  model: VRMModelInfo;
  onTest?: (model: VRMModelInfo) => void;
  onDownload?: (model: VRMModelInfo) => void;
  onLike?: (model: VRMModelInfo) => void;
  showDownloadStatus?: boolean;
}

const useStyles = createStyles(({ css, token }) => ({
  card: css`
    .ant-card-cover {
      height: 200px;
      overflow: hidden;
      position: relative;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
      
      &:hover img {
        transform: scale(1.05);
      }
    }
    
    .ant-card-body {
      padding: 16px;
    }
    
    .ant-card-actions {
      background: ${token.colorBgContainer};
      border-top: 1px solid ${token.colorBorderSecondary};
      
      .ant-card-actions > li {
        margin: 8px 0;
        
        .ant-btn {
          border: none;
          box-shadow: none;
        }
      }
    }
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
      transition: all 0.3s ease;
    }
  `,
  
  statusOverlay: css`
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 1;
  `,
  
  categoryTag: css`
    margin-bottom: 8px;
  `,
  
  modelInfo: css`
    margin-bottom: 8px;
    
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
      font-size: 12px;
      color: ${token.colorTextSecondary};
    }
  `,
  
  downloadProgress: css`
    margin-top: 8px;
  `,
  
  actionButton: css`
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    
    .anticon {
      font-size: 16px;
    }
  `
}));

const VRMModelCard: React.FC<VRMModelCardProps> = ({
  model,
  onTest,
  onDownload,
  onLike,
  showDownloadStatus = true
}) => {
  const { styles } = useStyles();
  const [liked, setLiked] = useState(false);
  const [downloading, setDownloading] = useState(false);
  
  // 检查是否已下载
  const isDownloaded = VRMModelService.isModelDownloaded(model.id);
  
  // 获取下载状态
  const downloadStatus = showDownloadStatus ? VRMModelService.getDownloadStatus(model.id) : null;

  // 处理测试
  const handleTest = () => {
    if (onTest) {
      onTest(model);
    } else {
      message.info(`测试 ${model.name} 的VRM模型...`);
    }
  };

  // 处理下载
  const handleDownload = async () => {
    if (isDownloaded) {
      message.info(`${model.name} 已经下载过了`);
      return;
    }

    try {
      setDownloading(true);
      
      if (onDownload) {
        onDownload(model);
      } else {
        await VRMModelService.downloadModel(model);
      }
    } catch (error) {
      console.error('下载失败:', error);
    } finally {
      setDownloading(false);
    }
  };

  // 处理点赞
  const handleLike = () => {
    setLiked(!liked);
    if (onLike) {
      onLike(model);
    }
  };

  // 渲染状态标签
  const renderStatusTag = () => {
    if (isDownloaded) {
      return (
        <Tag color="success" icon={<CheckCircleOutlined />}>
          已下载
        </Tag>
      );
    }
    
    if (model.vrmAccessible === false) {
      return (
        <Tag color="error" icon={<ExclamationCircleOutlined />}>
          不可用
        </Tag>
      );
    }
    
    if (model.vrmAccessible === true) {
      return (
        <Tag color="processing">
          可下载
        </Tag>
      );
    }
    
    return null;
  };

  // 渲染下载进度
  const renderDownloadProgress = () => {
    if (!downloadStatus || downloadStatus.status === 'completed') {
      return null;
    }

    let status: 'active' | 'exception' | 'success' = 'active';
    let statusText = '准备下载';

    switch (downloadStatus.status) {
      case 'downloading':
        status = 'active';
        statusText = '下载中...';
        break;
      case 'failed':
        status = 'exception';
        statusText = '下载失败';
        break;
      case 'completed':
        status = 'success';
        statusText = '下载完成';
        break;
    }

    return (
      <div className={styles.downloadProgress}>
        <Progress
          percent={downloadStatus.progress}
          status={status}
          size="small"
          format={() => statusText}
        />
      </div>
    );
  };

  const actions = [
    <Tooltip title="测试模型" key="test">
      <Button
        type="text"
        icon={<EyeOutlined />}
        onClick={handleTest}
        className={styles.actionButton}
        disabled={model.vrmAccessible === false}
      >
        测试
      </Button>
    </Tooltip>,
    
    <Tooltip title={isDownloaded ? "已下载" : "下载模型"} key="download">
      <Button
        type="text"
        icon={downloading ? <LoadingOutlined /> : <DownloadOutlined />}
        onClick={handleDownload}
        className={styles.actionButton}
        disabled={model.vrmAccessible === false || downloading}
        loading={downloading}
      >
        {isDownloaded ? '已下载' : '下载'}
      </Button>
    </Tooltip>,
    
    <Tooltip title={liked ? "取消收藏" : "收藏"} key="like">
      <Button
        type="text"
        icon={liked ? <HeartFilled style={{ color: '#ff4d4f' }} /> : <HeartOutlined />}
        onClick={handleLike}
        className={styles.actionButton}
      >
        {liked ? '已收藏' : '收藏'}
      </Button>
    </Tooltip>
  ];

  return (
    <Card
      className={styles.card}
      hoverable
      cover={
        <div style={{ position: 'relative' }}>
          <LazyImage
            src={processImageUrl(model.cover || model.avatar)}
            alt={model.name}
            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
          />
          <div className={styles.statusOverlay}>
            {renderStatusTag()}
          </div>
        </div>
      }
      actions={actions}
    >
      <Meta
        title={
          <div>
            <div style={{ marginBottom: 8 }}>
              {model.name}
              {model.rating && (
                <div style={{ float: 'right' }}>
                  <Rate disabled defaultValue={model.rating / 10} allowHalf size="small" />
                </div>
              )}
            </div>
            <Tag color="blue" className={styles.categoryTag}>
              {model.category}
            </Tag>
          </div>
        }
        description={
          <div>
            <p style={{ marginBottom: 8, fontSize: 13, lineHeight: 1.4 }}>
              {model.description}
            </p>
            
            <div className={styles.modelInfo}>
              {model.downloadCount && (
                <div className="info-item">
                  <span>下载量</span>
                  <span>{model.downloadCount.toLocaleString()}</span>
                </div>
              )}
              
              {model.fileSize && (
                <div className="info-item">
                  <span>文件大小</span>
                  <span>{model.fileSize}</span>
                </div>
              )}
              
              <div className="info-item">
                <span>来源</span>
                <span>{model.source}</span>
              </div>
            </div>
            
            {renderDownloadProgress()}
          </div>
        }
      />
    </Card>
  );
};

export default VRMModelCard;
