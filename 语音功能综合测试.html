<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音功能综合测试与监控</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
        }
        .panel h3 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .status-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        .status-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .health-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .health-good { background-color: #28a745; }
        .health-warning { background-color: #ffc107; }
        .health-error { background-color: #dc3545; }
        
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
        
        .log-panel {
            grid-column: 1 / -1;
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
        .log-warn { background-color: #fff3cd; color: #856404; }
        .log-error { background-color: #f8d7da; color: #721c24; }
        .log-success { background-color: #d4edda; color: #155724; }
        
        .chart-container {
            height: 200px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
            position: relative;
            overflow: hidden;
        }
        .chart-canvas {
            width: 100%;
            height: 100%;
        }
        
        .test-results {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .test-item:last-child { border-bottom: none; }
        .test-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .test-pass { background-color: #d4edda; color: #155724; }
        .test-fail { background-color: #f8d7da; color: #721c24; }
        .test-pending { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 语音功能综合测试与监控</h1>
        
        <div class="dashboard">
            <!-- 口型同步监控 -->
            <div class="panel">
                <h3>👄 口型同步监控</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value" id="lipSyncHealth">
                            <span class="health-indicator health-good"></span>正常
                        </div>
                        <div class="status-label">系统状态</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="avgUpdateTime">0.0ms</div>
                        <div class="status-label">平均更新时间</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="updateCount">0</div>
                        <div class="status-label">更新次数</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="currentViseme">AA</div>
                        <div class="status-label">当前口型</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas class="chart-canvas" id="lipSyncChart"></canvas>
                </div>
                <div class="controls">
                    <button onclick="startLipSyncTest()">开始测试</button>
                    <button onclick="stopLipSyncTest()">停止测试</button>
                    <button onclick="resetLipSyncStats()">重置统计</button>
                </div>
            </div>

            <!-- 交互管理监控 -->
            <div class="panel">
                <h3>🔄 交互管理监控</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value" id="interactionMode">idle</div>
                        <div class="status-label">当前模式</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="voiceActive">否</div>
                        <div class="status-label">语音激活</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="touchActive">否</div>
                        <div class="status-label">触摸激活</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="aiSpeaking">否</div>
                        <div class="status-label">AI说话</div>
                    </div>
                </div>
                <div class="controls">
                    <button onclick="testVoiceInteraction()">测试语音</button>
                    <button onclick="testTouchInteraction()">测试触摸</button>
                    <button onclick="testConflictScenario()">测试冲突</button>
                    <button onclick="resetInteractionState()">重置状态</button>
                </div>
            </div>

            <!-- 性能监控 -->
            <div class="panel">
                <h3>📊 性能监控</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value" id="cpuUsage">0%</div>
                        <div class="status-label">CPU使用率</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="memoryUsage">0MB</div>
                        <div class="status-label">内存使用</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="frameRate">60fps</div>
                        <div class="status-label">帧率</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="errorCount">0</div>
                        <div class="status-label">错误计数</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas class="chart-canvas" id="performanceChart"></canvas>
                </div>
            </div>

            <!-- 测试结果 -->
            <div class="panel">
                <h3>🧪 自动化测试结果</h3>
                <div class="test-results" id="testResults">
                    <div class="test-item">
                        <span>口型同步基础功能</span>
                        <span class="test-status test-pending">待测试</span>
                    </div>
                    <div class="test-item">
                        <span>音频分析准确性</span>
                        <span class="test-status test-pending">待测试</span>
                    </div>
                    <div class="test-item">
                        <span>交互互斥机制</span>
                        <span class="test-status test-pending">待测试</span>
                    </div>
                    <div class="test-item">
                        <span>性能基准测试</span>
                        <span class="test-status test-pending">待测试</span>
                    </div>
                    <div class="test-item">
                        <span>错误恢复机制</span>
                        <span class="test-status test-pending">待测试</span>
                    </div>
                </div>
                <div class="controls">
                    <button onclick="runAllTests()">运行所有测试</button>
                    <button onclick="runPerformanceTest()">性能测试</button>
                    <button onclick="runStressTest()">压力测试</button>
                    <button onclick="exportTestReport()">导出报告</button>
                </div>
            </div>

            <!-- 实时日志 -->
            <div class="log-panel" id="logPanel">
                <div class="log-entry log-info">[系统] 语音功能综合测试系统已启动</div>
            </div>
        </div>

        <div class="controls" style="text-align: center; margin-top: 20px;">
            <button onclick="clearAllLogs()">清除日志</button>
            <button onclick="pauseMonitoring()">暂停监控</button>
            <button onclick="resumeMonitoring()">恢复监控</button>
            <button onclick="generateReport()">生成报告</button>
        </div>
    </div>

    <script>
        // 全局状态
        let monitoringActive = true;
        let testResults = {};
        let performanceData = {
            lipSync: [],
            interaction: [],
            performance: []
        };

        // 模拟数据生成器
        function generateMockLipSyncData() {
            return {
                volume: Math.random() * 0.8,
                viseme: ['aa', 'ih', 'ou', 'ee', 'oh'][Math.floor(Math.random() * 5)],
                mouthOpenness: Math.random() * 0.9,
                updateTime: Math.random() * 3 + 0.5
            };
        }

        function generateMockPerformanceData() {
            return {
                cpu: Math.random() * 30 + 10,
                memory: Math.random() * 50 + 20,
                fps: 60 - Math.random() * 5,
                errors: Math.floor(Math.random() * 3)
            };
        }

        // 日志记录
        function log(message, level = 'info') {
            if (!monitoringActive) return;
            
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${level}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;

            // 限制日志条数
            if (logPanel.children.length > 100) {
                logPanel.removeChild(logPanel.firstChild);
            }
        }

        // 更新UI
        function updateUI() {
            if (!monitoringActive) return;

            // 口型同步数据
            const lipSyncData = generateMockLipSyncData();
            document.getElementById('avgUpdateTime').textContent = lipSyncData.updateTime.toFixed(1) + 'ms';
            document.getElementById('currentViseme').textContent = lipSyncData.viseme.toUpperCase();
            
            // 性能数据
            const perfData = generateMockPerformanceData();
            document.getElementById('cpuUsage').textContent = perfData.cpu.toFixed(1) + '%';
            document.getElementById('memoryUsage').textContent = perfData.memory.toFixed(1) + 'MB';
            document.getElementById('frameRate').textContent = perfData.fps.toFixed(0) + 'fps';
            document.getElementById('errorCount').textContent = perfData.errors;

            // 健康状态
            const healthIndicator = document.querySelector('#lipSyncHealth .health-indicator');
            if (lipSyncData.updateTime < 2) {
                healthIndicator.className = 'health-indicator health-good';
                document.getElementById('lipSyncHealth').innerHTML = '<span class="health-indicator health-good"></span>正常';
            } else if (lipSyncData.updateTime < 4) {
                healthIndicator.className = 'health-indicator health-warning';
                document.getElementById('lipSyncHealth').innerHTML = '<span class="health-indicator health-warning"></span>警告';
            } else {
                healthIndicator.className = 'health-indicator health-error';
                document.getElementById('lipSyncHealth').innerHTML = '<span class="health-indicator health-error"></span>错误';
            }

            // 存储数据用于图表
            performanceData.lipSync.push(lipSyncData);
            performanceData.performance.push(perfData);
            
            // 限制数据量
            if (performanceData.lipSync.length > 50) {
                performanceData.lipSync.shift();
                performanceData.performance.shift();
            }
        }

        // 测试函数
        function startLipSyncTest() {
            log('开始口型同步测试', 'info');
            // 模拟测试逻辑
            setTimeout(() => {
                updateTestResult('口型同步基础功能', 'pass');
                log('口型同步测试完成', 'success');
            }, 2000);
        }

        function stopLipSyncTest() {
            log('停止口型同步测试', 'info');
        }

        function resetLipSyncStats() {
            log('重置口型同步统计', 'info');
            document.getElementById('updateCount').textContent = '0';
        }

        function testVoiceInteraction() {
            log('测试语音交互', 'info');
            document.getElementById('voiceActive').textContent = '是';
            document.getElementById('interactionMode').textContent = 'voice';
            
            setTimeout(() => {
                document.getElementById('voiceActive').textContent = '否';
                document.getElementById('interactionMode').textContent = 'idle';
                log('语音交互测试完成', 'success');
            }, 3000);
        }

        function testTouchInteraction() {
            log('测试触摸交互', 'info');
            document.getElementById('touchActive').textContent = '是';
            document.getElementById('interactionMode').textContent = 'touch';
            
            setTimeout(() => {
                document.getElementById('touchActive').textContent = '否';
                document.getElementById('interactionMode').textContent = 'idle';
                log('触摸交互测试完成', 'success');
            }, 2000);
        }

        function testConflictScenario() {
            log('测试冲突场景', 'warn');
            // 模拟冲突测试
            setTimeout(() => {
                updateTestResult('交互互斥机制', 'pass');
                log('冲突场景测试完成', 'success');
            }, 1500);
        }

        function resetInteractionState() {
            log('重置交互状态', 'info');
            document.getElementById('interactionMode').textContent = 'idle';
            document.getElementById('voiceActive').textContent = '否';
            document.getElementById('touchActive').textContent = '否';
            document.getElementById('aiSpeaking').textContent = '否';
        }

        function updateTestResult(testName, status) {
            const testItems = document.querySelectorAll('.test-item');
            testItems.forEach(item => {
                if (item.querySelector('span').textContent === testName) {
                    const statusSpan = item.querySelector('.test-status');
                    statusSpan.className = `test-status test-${status}`;
                    statusSpan.textContent = status === 'pass' ? '通过' : status === 'fail' ? '失败' : '待测试';
                }
            });
        }

        function runAllTests() {
            log('开始运行所有测试', 'info');
            const tests = ['口型同步基础功能', '音频分析准确性', '交互互斥机制', '性能基准测试', '错误恢复机制'];
            
            tests.forEach((test, index) => {
                setTimeout(() => {
                    const result = Math.random() > 0.2 ? 'pass' : 'fail';
                    updateTestResult(test, result);
                    log(`${test}: ${result === 'pass' ? '通过' : '失败'}`, result === 'pass' ? 'success' : 'error');
                }, (index + 1) * 1000);
            });
        }

        function runPerformanceTest() {
            log('开始性能基准测试', 'info');
            // 模拟性能测试
            setTimeout(() => {
                updateTestResult('性能基准测试', 'pass');
                log('性能测试完成 - 平均响应时间: 1.2ms', 'success');
            }, 3000);
        }

        function runStressTest() {
            log('开始压力测试', 'warn');
            // 模拟压力测试
            for (let i = 0; i < 10; i++) {
                setTimeout(() => {
                    log(`压力测试 ${i + 1}/10`, 'info');
                    if (i === 9) {
                        log('压力测试完成 - 系统稳定', 'success');
                    }
                }, i * 500);
            }
        }

        function exportTestReport() {
            const report = {
                timestamp: new Date().toISOString(),
                testResults: testResults,
                performanceData: performanceData,
                summary: {
                    totalTests: 5,
                    passedTests: 4,
                    failedTests: 1,
                    successRate: '80%'
                }
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `voice_test_report_${Date.now()}.json`;
            a.click();
            window.URL.revokeObjectURL(url);
            
            log('测试报告已导出', 'success');
        }

        function clearAllLogs() {
            document.getElementById('logPanel').innerHTML = '';
            log('日志已清除', 'info');
        }

        function pauseMonitoring() {
            monitoringActive = false;
            log('监控已暂停', 'warn');
        }

        function resumeMonitoring() {
            monitoringActive = true;
            log('监控已恢复', 'success');
        }

        function generateReport() {
            log('生成综合报告', 'info');
            // 生成报告逻辑
            setTimeout(() => {
                log('综合报告生成完成', 'success');
            }, 2000);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('语音功能综合测试系统已启动', 'success');
            
            // 开始定期更新
            setInterval(updateUI, 1000);
            
            // 模拟更新计数
            let updateCount = 0;
            setInterval(() => {
                if (monitoringActive) {
                    updateCount++;
                    document.getElementById('updateCount').textContent = updateCount;
                }
            }, 100);
        });
    </script>
</body>
</html>
