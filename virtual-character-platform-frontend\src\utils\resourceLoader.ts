/**
 * 资源加载容错工具
 * 提供网络资源加载失败时的重试机制和降级方案
 */

export interface LoadOptions {
  maxRetries?: number;
  retryDelay?: number;
  timeout?: number;
  fallbackUrls?: string[];
  onProgress?: (progress: number) => void;
  onRetry?: (attempt: number, error: Error) => void;
}

export interface LoadResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  attempts: number;
  finalUrl?: string;
}

/**
 * 延迟函数
 */
const delay = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * 带超时的fetch请求
 */
const fetchWithTimeout = async (url: string, timeout: number): Promise<Response> => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  try {
    const response = await fetch(url, {
      signal: controller.signal,
      cache: 'no-cache'
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
};

/**
 * 通用资源加载器
 */
export class ResourceLoader {
  private static defaultOptions: Required<LoadOptions> = {
    maxRetries: 3,
    retryDelay: 1000,
    timeout: 10000,
    fallbackUrls: [],
    onProgress: () => {},
    onRetry: () => {}
  };

  /**
   * 加载文本资源
   */
  static async loadText(url: string, options: LoadOptions = {}): Promise<LoadResult<string>> {
    const opts = { ...this.defaultOptions, ...options };
    const urlsToTry = [url, ...opts.fallbackUrls];
    
    let lastError: Error | undefined;
    let attempts = 0;

    for (const currentUrl of urlsToTry) {
      for (let retry = 0; retry <= opts.maxRetries; retry++) {
        attempts++;
        
        try {
          console.log(`🔄 尝试加载文本资源: ${currentUrl} (第${attempts}次尝试)`);
          
          const response = await fetchWithTimeout(currentUrl, opts.timeout);
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          
          const text = await response.text();
          console.log(`✅ 文本资源加载成功: ${currentUrl}`);
          
          return {
            success: true,
            data: text,
            attempts,
            finalUrl: currentUrl
          };
          
        } catch (error) {
          lastError = error as Error;
          console.warn(`❌ 文本资源加载失败: ${currentUrl}, 错误: ${lastError.message}`);
          
          opts.onRetry(attempts, lastError);
          
          if (retry < opts.maxRetries) {
            console.log(`⏳ ${opts.retryDelay}ms后重试...`);
            await delay(opts.retryDelay);
          }
        }
      }
    }

    return {
      success: false,
      error: lastError,
      attempts
    };
  }

  /**
   * 加载二进制资源（如VRM模型）
   */
  static async loadBinary(url: string, options: LoadOptions = {}): Promise<LoadResult<ArrayBuffer>> {
    const opts = { ...this.defaultOptions, ...options };
    const urlsToTry = [url, ...opts.fallbackUrls];
    
    let lastError: Error | undefined;
    let attempts = 0;

    for (const currentUrl of urlsToTry) {
      for (let retry = 0; retry <= opts.maxRetries; retry++) {
        attempts++;
        
        try {
          console.log(`🔄 尝试加载二进制资源: ${currentUrl} (第${attempts}次尝试)`);
          
          const response = await fetchWithTimeout(currentUrl, opts.timeout);
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          
          const buffer = await response.arrayBuffer();
          console.log(`✅ 二进制资源加载成功: ${currentUrl} (${buffer.byteLength} bytes)`);
          
          return {
            success: true,
            data: buffer,
            attempts,
            finalUrl: currentUrl
          };
          
        } catch (error) {
          lastError = error as Error;
          console.warn(`❌ 二进制资源加载失败: ${currentUrl}, 错误: ${lastError.message}`);
          
          opts.onRetry(attempts, lastError);
          
          if (retry < opts.maxRetries) {
            console.log(`⏳ ${opts.retryDelay}ms后重试...`);
            await delay(opts.retryDelay);
          }
        }
      }
    }

    return {
      success: false,
      error: lastError,
      attempts
    };
  }

  /**
   * 加载图片资源
   */
  static async loadImage(url: string, options: LoadOptions = {}): Promise<LoadResult<HTMLImageElement>> {
    const opts = { ...this.defaultOptions, ...options };
    const urlsToTry = [url, ...opts.fallbackUrls];
    
    let lastError: Error | undefined;
    let attempts = 0;

    for (const currentUrl of urlsToTry) {
      for (let retry = 0; retry <= opts.maxRetries; retry++) {
        attempts++;
        
        try {
          console.log(`🔄 尝试加载图片资源: ${currentUrl} (第${attempts}次尝试)`);
          
          const img = await new Promise<HTMLImageElement>((resolve, reject) => {
            const image = new Image();
            image.crossOrigin = 'anonymous';
            
            const timeoutId = setTimeout(() => {
              reject(new Error('图片加载超时'));
            }, opts.timeout);
            
            image.onload = () => {
              clearTimeout(timeoutId);
              resolve(image);
            };
            
            image.onerror = () => {
              clearTimeout(timeoutId);
              reject(new Error('图片加载失败'));
            };
            
            image.src = currentUrl;
          });
          
          console.log(`✅ 图片资源加载成功: ${currentUrl}`);
          
          return {
            success: true,
            data: img,
            attempts,
            finalUrl: currentUrl
          };
          
        } catch (error) {
          lastError = error as Error;
          console.warn(`❌ 图片资源加载失败: ${currentUrl}, 错误: ${lastError.message}`);
          
          opts.onRetry(attempts, lastError);
          
          if (retry < opts.maxRetries) {
            console.log(`⏳ ${opts.retryDelay}ms后重试...`);
            await delay(opts.retryDelay);
          }
        }
      }
    }

    return {
      success: false,
      error: lastError,
      attempts
    };
  }

  /**
   * 检查资源是否可访问
   */
  static async checkResourceAvailability(url: string, timeout: number = 5000): Promise<boolean> {
    try {
      const response = await fetchWithTimeout(url, timeout);
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 批量检查资源可用性
   */
  static async checkMultipleResources(urls: string[], timeout: number = 5000): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    
    await Promise.all(
      urls.map(async (url) => {
        results[url] = await this.checkResourceAvailability(url, timeout);
      })
    );
    
    return results;
  }
}

/**
 * VRM模型专用加载器
 */
export class VRMLoader extends ResourceLoader {
  /**
   * 加载VRM模型，带有特定的容错机制
   */
  static async loadVRMModel(url: string, options: LoadOptions = {}): Promise<LoadResult<ArrayBuffer>> {
    // VRM模型的备用URL策略
    const fallbackUrls = [
      // 可以添加CDN或镜像URL
      ...options.fallbackUrls || []
    ];
    
    const vrmOptions: LoadOptions = {
      maxRetries: 5, // VRM模型重试次数更多
      retryDelay: 2000, // 重试间隔更长
      timeout: 30000, // 超时时间更长
      fallbackUrls,
      onRetry: (attempt, error) => {
        console.warn(`🔄 VRM模型加载重试 ${attempt}: ${error.message}`);
        options.onRetry?.(attempt, error);
      },
      ...options
    };
    
    return this.loadBinary(url, vrmOptions);
  }
}
