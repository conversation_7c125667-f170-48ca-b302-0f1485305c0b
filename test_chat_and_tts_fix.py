#!/usr/bin/env python3
"""
测试聊天和TTS修复效果的脚本
"""
import requests
import json
import time

def test_character_chat_with_tts():
    """测试角色聊天和TTS功能"""
    
    print("🧪 测试角色聊天和TTS功能")
    print("=" * 50)
    
    # 1. 先登录获取token
    login_url = "http://localhost:8000/api/auth/login/"
    login_data = {
        "username": "api_test_user",
        "password": "test_password_123"
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
            
        token = login_response.json().get('token')
        if not token:
            print("❌ 未获取到token")
            return False
            
        print(f"✅ 登录成功")
        
        # 2. 测试角色聊天
        chat_url = "http://localhost:8000/api/characters/1/chat/"
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        test_cases = [
            {
                "name": "文字聊天测试",
                "data": {
                    "user_message": "你好，请简单介绍一下你自己",
                    "enable_tts": True,
                    "voice_mode": False
                }
            },
            {
                "name": "语音聊天测试",
                "data": {
                    "user_message": "今天天气真好，我很开心",
                    "enable_tts": True,
                    "voice_mode": True
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}️⃣ {test_case['name']}")
            print(f"📤 发送消息: {test_case['data']['user_message']}")
            
            response = requests.post(chat_url, json=test_case['data'], headers=headers)
            
            if response.status_code == 200:
                response_data = response.json()
                print("✅ 聊天请求成功!")
                print(f"🤖 AI回复: {response_data.get('character_response', 'N/A')[:100]}...")
                
                # 检查TTS相关字段
                if response_data.get('audio_url'):
                    print(f"🔊 语音URL: {response_data['audio_url']}")
                else:
                    print("⚠️ 未返回语音URL")
                    
                if response_data.get('emotion_analysis'):
                    emotion = response_data['emotion_analysis']
                    print(f"🎭 情感分析: {emotion}")
                else:
                    print("⚠️ 未返回情感分析")
                    
            else:
                print(f"❌ 聊天请求失败: {response.status_code}")
                print(f"错误内容: {response.text}")
                
        # 3. 测试Edge TTS API
        print(f"\n3️⃣ 测试Edge TTS API")
        tts_url = "http://localhost:8000/api/voice/edge/"
        
        tts_data = {
            "input": "你好，这是语音合成测试",
            "options": {
                "voice": "zh-CN-XiaoxiaoNeural",
                "rate": 0,
                "pitch": 0
            }
        }
        
        print(f"📤 发送TTS请求...")
        tts_response = requests.post(tts_url, json=tts_data, headers=headers)
        
        if tts_response.status_code == 200:
            print("✅ Edge TTS API正常工作!")
            print(f"📄 响应大小: {len(tts_response.content)} bytes")
            
            # 检查是否是音频数据
            content_type = tts_response.headers.get('content-type', '')
            if 'audio' in content_type:
                print(f"🔊 返回音频数据，类型: {content_type}")
            else:
                print(f"⚠️ 返回数据类型: {content_type}")
        else:
            print(f"❌ Edge TTS API失败: {tts_response.status_code}")
            print(f"错误内容: {tts_response.text}")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_generic_chat_api():
    """测试通用聊天API"""
    
    print(f"\n4️⃣ 测试通用聊天API")
    
    # 登录
    login_url = "http://localhost:8000/api/auth/login/"
    login_data = {
        "username": "api_test_user",
        "password": "test_password_123"
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        token = login_response.json().get('token')
        
        # 测试通用聊天
        chat_url = "http://localhost:8000/api/chat/spark/"
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        test_data = {
            "model": "spark-4.0-ultra",
            "messages": [
                {
                    "role": "user",
                    "content": "你好，请简单介绍一下你自己"
                }
            ],
            "stream": False
        }
        
        response = requests.post(chat_url, json=test_data, headers=headers)
        
        if response.status_code == 200:
            print("✅ 通用聊天API正常工作!")
            response_data = response.json()
            content = response_data['choices'][0]['message']['content']
            print(f"🤖 AI回复: {content[:100]}...")
        else:
            print(f"❌ 通用聊天API失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 通用聊天API测试失败: {e}")

def main():
    """主测试函数"""
    print("🎯 聊天和TTS修复效果测试")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 执行测试
    success = test_character_chat_with_tts()
    test_generic_chat_api()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    if success:
        print("✅ 主要功能测试通过")
        print("🎉 修复效果验证成功！")
        print("\n🔧 修复内容:")
        print("   - ✅ 设置chatMode为'camera'模式")
        print("   - ✅ 启用文字回复的语音播放")
        print("   - ✅ 修复TTS配置格式")
        print("   - ✅ 确保Edge TTS API正常工作")
    else:
        print("❌ 部分功能仍有问题，需要进一步检查")

if __name__ == "__main__":
    main()
