import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Button, Spin, Modal, App } from 'antd';
import { LogoutOutlined, MessageOutlined, SettingOutlined, AudioOutlined, AudioMutedOutlined } from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';

import useAuthStore from '../store/authStore';
import AgentViewer from '../features/AgentViewer';
import { VoiceControls } from '../components/VoiceControls';
import BottomChatBox from '../components/chat/BottomChatBox';
import { useSpeechRecognition } from '../hooks/useSpeechRecognition';
import { useGlobalStore } from '../store/global';
import { useInteractionManager } from '../hooks/useInteractionManager';
import TouchInteractionWrapper from '../components/TouchInteractionWrapper';
import SpeechRecognitionErrorBoundary from '../components/SpeechRecognitionErrorBoundary';

// 新的模块化Hooks
import { useCharacterLoader } from '../hooks/useCharacterLoader';
import { useAutoVRMLoader } from '../hooks/useVRMModel';
import { useAutoGreeting } from '../hooks/useGreeting';
import { useChatInteraction } from '../hooks/useChatInteraction';

import '../styles/voice-controls.css';
import '../styles/vidol-chat.css';
import '../styles/enhanced-immersive.css';

interface Character {
  id: string;
  name: string;
  description: string;
  vrmModelUrl?: string;
  greeting?: string;
  settings?: {
    voice_type?: string;
    animation_style?: string;
  };
  vrm_model_config?: {
    voice_type?: string;
    animation_style?: string;
    greeting?: string;
    systemRole?: string;
  };
  system_role?: string;
  image_url?: string;
  cover?: string;
}

const EnhancedImmersiveChatPage: React.FC = () => {
  const { characterId } = useParams<{ characterId: string }>();
  const navigate = useNavigate();
  const { userInfo } = useAuthStore();
  const { message } = App.useApp();

  // UI状态
  const [isListening, setIsListening] = useState(false);
  const [characterEmotion, setCharacterEmotion] = useState('neutral');
  const [showHelp, setShowHelp] = useState(false);
  const [isFirstTime, setIsFirstTime] = useState(true);
  const [chatBoxMode, setChatBoxMode] = useState<'minimal' | 'expanded' | 'hidden'>('minimal');
  const [showSettings, setShowSettings] = useState(false);

  // 背景图片状态
  const [backgroundImageUrl, setBackgroundImageUrl] = useState<string>('');
  const [backgroundType, setBackgroundType] = useState<'character' | 'default' | 'gradient'>('default');
  const [backgroundOpacity, setBackgroundOpacity] = useState(0.8);

  // 麦克风权限状态
  const [microphonePermission, setMicrophonePermission] = useState<'granted' | 'denied' | 'prompt' | 'unknown'>('unknown');

  // 使用新的模块化Hooks
  const {
    selectedCharacter,
    selectedVrmModel,
    loading,
    error,
    loadCharacter,
  } = useCharacterLoader({
    onCharacterLoaded: (character) => {
      console.log('角色加载完成:', character.name);
    },
    onVRMModelSet: (model) => {
      console.log('VRM模型设置完成:', model.name);
    },
    onBackgroundSet: (url, type) => {
      setBackgroundImageUrl(url);
      setBackgroundType(type);
    },
  });

  // 全局状态
  const { voiceOn, setVoiceOn, interactive, setInteractive, viewer, setChatMode } = useGlobalStore();

  const { vrmModelLoaded } = useAutoVRMLoader(selectedVrmModel, viewer, {
    onLoadSuccess: (model) => {
      console.log('VRM模型加载成功:', model.name);
    },
    onLoadError: (error, model) => {
      console.error('VRM模型加载失败:', error, model.name);
    },
  });

  // 交互状态管理 - 需要在useChatInteraction之前定义
  const interactionManager = useInteractionManager(
    {
      voicePriority: true,
      touchTimeout: 10000,
      voiceTimeout: 60000,
      enableLogging: true,
    },
    {
      onVoiceStart: () => {
        console.log('🎤 语音交互开始');
        setIsListening(true);
      },
      onVoiceEnd: () => {
        console.log('🎤 语音交互结束');
        setIsListening(false);
      },
      onTouchStart: () => {
        console.log('👆 触摸交互开始');
      },
      onTouchEnd: () => {
        console.log('👆 触摸交互结束');
      },
      onModeChange: (mode) => {
        console.log('🔄 交互模式变更:', mode);
      },
      onConflict: (currentMode, requestedMode) => {
        console.log('⚠️ 交互冲突:', currentMode, '→', requestedMode);
      },
    }
  );

  // 打招呼功能 - 使用稳定的回调函数引用
  const handleEmotionChange = useCallback((emotion: string) => {
    setCharacterEmotion(emotion);
  }, []);

  const { resetGreeting } = useAutoGreeting(
    selectedCharacter,
    vrmModelLoaded,
    loading,
    {
      onEmotionChange: handleEmotionChange,
      viewer,
      vrmModelLoaded,
    }
  );

  // 聊天交互功能
  const {
    isProcessing,
    isAISpeaking,
    lastAIResponse,
    handleVoiceInput,
    handleTextMessage,
  } = useChatInteraction({
    character: selectedCharacter,
    interactionManager, // 传递interactionManager
    onEmotionChange: handleEmotionChange,
    onAISpeakingChange: (speaking) => {
      console.log('AI说话状态变化:', speaking);
    },
    onLastResponseChange: (response) => {
      console.log('AI回复记录:', response);
    },
    onChatBoxModeChange: setChatBoxMode,
  });

  // 性能监控和无限循环检测
  const renderCountRef = useRef(0);
  const lastRenderTimeRef = useRef(Date.now());

  useEffect(() => {
    renderCountRef.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTimeRef.current;

    console.log(`🔄 EnhancedImmersiveChatPage 渲染次数: ${renderCountRef.current}`);

    // 检测无限循环：如果在5秒内渲染超过50次，发出警告
    if (renderCountRef.current > 50 && timeSinceLastRender < 5000) {
      console.error('🚨 检测到可能的无限渲染循环！渲染次数:', renderCountRef.current);
      message.error('检测到页面异常重渲染，请刷新页面');
    }

    // 每100次渲染重置计数器，避免数字过大
    if (renderCountRef.current % 100 === 0) {
      lastRenderTimeRef.current = now;
    }
  });

  // 背景样式计算
  const backgroundStyle = useMemo(() => {
    switch (backgroundType) {
      case 'character':
        return {
          backgroundImage: backgroundImageUrl ? `url(${backgroundImageUrl})` : 'none',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          opacity: backgroundOpacity
        };
      case 'gradient':
        return {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          opacity: backgroundOpacity
        };
      default:
        return {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          opacity: backgroundOpacity
        };
    }
  }, [backgroundType, backgroundImageUrl, backgroundOpacity]);

  // 语音识别支持检查
  const { isSupported: speechSupported, error: speechError } = useSpeechRecognition();

  // 会话状态 - 暂时注释掉未使用的变量
  // const [currentChats] = useSessionStore(
  //   (s) => [
  //     sessionSelectors.currentChats(s),
  //   ],
  //   isEqual,
  // );



  // 设置沉浸式模式的chatMode
  useEffect(() => {
    setChatMode('camera');
    return () => {
      setChatMode('chat');
    };
  }, [setChatMode]);

  // 加载角色数据 - 添加防抖机制
  useEffect(() => {
    if (!characterId) return;

    console.log('🔄 准备加载角色:', characterId);

    // 防抖延迟，避免快速切换时重复加载
    const timeoutId = setTimeout(() => {
      loadCharacter(characterId);
    }, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [characterId]); // 移除loadCharacter依赖，避免循环

  // 欢迎语音现在由useAutoGreeting自动处理，无需手动触发

  // 检查麦克风权限
  useEffect(() => {
    const checkMicrophonePermission = async () => {
      try {
        if (navigator.permissions) {
          const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
          setMicrophonePermission(permission.state);
          console.log('麦克风权限状态:', permission.state);

          permission.addEventListener('change', () => {
            setMicrophonePermission(permission.state);
            console.log('麦克风权限状态变化:', permission.state);
          });
        } else {
          console.log('浏览器不支持权限查询API');
        }
      } catch (error) {
        console.error('检查麦克风权限失败:', error);
      }
    };

    checkMicrophonePermission();
  }, []);

  // 首次使用提示
  useEffect(() => {
    if (!loading && selectedCharacter && isFirstTime) {
      setShowHelp(true);
      setIsFirstTime(false);
    }
  }, [loading, selectedCharacter, isFirstTime]);

  // 定期自动检查和修复VRM材质状态
  useEffect(() => {
    const materialCheckInterval = setInterval(() => {
      if (viewer?.model?.vrm) {
        let hasTransparentMaterial = false;
        viewer.model.vrm.scene.traverse((child: any) => {
          if (child.type === 'Mesh' && child.material) {
            if (child.material.opacity !== 1.0 || child.material.transparent !== false) {
              console.log('🔧 定期检查发现透明材质，自动修复:', child.name);
              child.material.opacity = 1.0;
              child.material.transparent = false;
              child.material.alphaTest = 0;
              child.material.needsUpdate = true;
              hasTransparentMaterial = true;
            }
          }
        });

        if (hasTransparentMaterial) {
          console.log('🔧 定期材质检查完成，已修复透明材质');
        }
      }
    }, 5000); // 每5秒检查一次

    return () => {
      clearInterval(materialCheckInterval);
    };
  }, [viewer]);






  // 语音监听状态变化处理
  const handleListeningChange = useCallback((listening: boolean) => {
    setIsListening(listening);
    if (listening) {
      setCharacterEmotion('listening');
    } else if (!isProcessing) {
      setCharacterEmotion('neutral');
    }
  }, [isProcessing]);

  // UI控制函数
  const handleChatBoxModeChange = useCallback((mode: 'minimal' | 'expanded' | 'hidden') => {
    setChatBoxMode(mode);
  }, []);

  const exitImmersiveMode = useCallback(() => {
    navigate('/');
  }, [navigate]);

  const toggleVoiceMode = useCallback(() => {
    setVoiceOn(!voiceOn);
    if (!voiceOn) {
      setChatBoxMode('minimal');
      message.success('语音模式已开启');
    } else {
      message.success('语音模式已关闭');
    }
  }, [voiceOn, setVoiceOn, message]);

  const toggleChatBox = useCallback(() => {
    const newMode = chatBoxMode === 'hidden' ? 'minimal' : 'hidden';
    setChatBoxMode(newMode);
  }, [chatBoxMode]);

  if (loading) {
    return (
      <div className="enhanced-immersive-chat" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          color: 'white'
        }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px', fontSize: '16px' }}>
            正在加载...
          </div>
        </div>
      </div>
    );
  }

  if (error || !selectedCharacter) {
    return (
      <div className="enhanced-immersive-chat" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          color: 'white'
        }}>
          <h3 style={{ color: 'white', marginBottom: '16px' }}>加载失败</h3>
          <p style={{ marginBottom: '24px' }}>{error || '角色不存在'}</p>
          <Button type="primary" onClick={() => navigate('/')}>
            返回首页
          </Button>
        </div>
      </div>
    );
  }



  return (
    <div className="enhanced-immersive-chat">
      {/* 背景层 */}
      <div
        className="enhanced-immersive-background"
        style={backgroundStyle}
      />

      {/* 内容层 */}
      <div className="enhanced-immersive-content">
        {/* 顶部控制栏 */}
        <div className="enhanced-immersive-top-bar">
          <div className="top-controls">
            <Button
              type="text"
              icon={<LogoutOutlined />}
              onClick={exitImmersiveMode}
              className="control-button"
              title="退出"
            />
            <Button
              type="text"
              icon={<MessageOutlined />}
              onClick={toggleChatBox}
              className={`control-button ${chatBoxMode !== 'hidden' ? 'active' : ''}`}
              title={chatBoxMode === 'hidden' ? '显示聊天框' : '隐藏聊天框'}
            />
            <Button
              type="text"
              icon={voiceOn ? <AudioOutlined /> : <AudioMutedOutlined />}
              onClick={toggleVoiceMode}
              className={`control-button ${voiceOn ? 'active' : ''}`}
              title={voiceOn ? '关闭语音模式' : '开启语音模式'}
            />
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => setShowSettings(true)}
              className="control-button"
              title="设置"
            />


          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="enhanced-immersive-main">
          {/* 3D角色展示区域 */}
          <div className="enhanced-immersive-3d-area">
            <TouchInteractionWrapper
              disabled={interactionManager.isVoiceActive}
              showWarning={true}
              warningMessage={
                interactionManager.isVoiceActive
                  ? "语音交互进行中，触摸功能暂时不可用"
                  : "触摸功能可用"
              }
              preventBubbling={true}
              logInterceptions={true}
              callbacks={{
                onTouchIntercepted: (eventInfo) => {
                  console.log('🚫 触摸事件被拦截:', eventInfo);
                },
                onTouchAllowed: (eventInfo) => {
                  // 开始触摸交互
                  const result = interactionManager.startTouchInteraction();
                  if (result.success) {
                    console.log('👆 触摸交互开始:', eventInfo);
                    // 设置超时自动结束触摸交互
                    setTimeout(() => {
                      interactionManager.endTouchInteraction();
                    }, 3000); // 3秒后自动结束
                  }
                },
              }}
            >
              <AgentViewer
                key={`agent-viewer-${selectedCharacter?.id}`}
                agentId={selectedCharacter?.id || ''}
                interactive={interactive}
                toolbar={false}
                height="100vh"
                width="100%"
              />
            </TouchInteractionWrapper>

            {/* 语音控制悬浮在3D区域 */}
            {voiceOn && (
              <SpeechRecognitionErrorBoundary
                onError={(error, errorInfo) => {
                  console.error('🚨 语音控制组件错误:', error);
                  console.error('📍 错误组件栈:', errorInfo.componentStack);
                }}
                fallback={
                  <div className="enhanced-immersive-voice-controls">
                    <div style={{
                      padding: '10px',
                      backgroundColor: '#fff2f0',
                      border: '1px solid #ffccc7',
                      borderRadius: '6px',
                      textAlign: 'center'
                    }}>
                      <span style={{ color: '#cf1322' }}>
                        🎤 语音功能暂时不可用
                      </span>
                    </div>
                  </div>
                }
              >
                <div className="enhanced-immersive-voice-controls">
                  <VoiceControls
                    onVoiceInput={handleVoiceInput}
                    onListeningChange={handleListeningChange}
                    disabled={isProcessing || !speechSupported}
                    className="enhanced-voice-input"
                    isAISpeaking={isAISpeaking}
                    lastAIResponse={lastAIResponse}
                    onPauseForAI={() => console.log('🔇 为AI播放暂停语音识别')}
                    onResumeAfterAI={() => console.log('🎤 AI播放完成，恢复语音识别')}
                  />

                  {isProcessing && (
                    <div className="enhanced-processing-indicator">
                      <Spin size="small" />
                    </div>
                  )}
                </div>
              </SpeechRecognitionErrorBoundary>
            )}
          </div>
        </div>

        {/* 底部聊天框 */}
        <BottomChatBox
          mode={chatBoxMode}
          onModeChange={handleChatBoxModeChange}
          onSendMessage={handleTextMessage}
          showHistory={true}
        />

        {/* 帮助模态框 */}
        <Modal
          title="🎤 沉浸式聊天体验"
          open={showHelp}
          onCancel={() => setShowHelp(false)}
          footer={[
            <Button key="close" type="primary" onClick={() => setShowHelp(false)}>
              开始体验
            </Button>
          ]}
          width={400}
        >
          <div className="help-content">
            <p>欢迎来到与 <strong>{selectedCharacter.name}</strong> 的沉浸式聊天体验！</p>
            <p>💬 使用底部聊天框进行文字对话</p>
            <p>🎤 点击语音按钮进行语音交流</p>
            <p>⚙️ 点击右上角设置按钮调整偏好</p>

            {!speechSupported && (
              <div style={{ color: 'red', marginTop: '10px', padding: '8px', backgroundColor: '#fff2f0', border: '1px solid #ffccc7', borderRadius: '4px' }}>
                ❌ 当前浏览器不支持语音识别功能，请使用Chrome、Edge或Safari浏览器。
              </div>
            )}

            {speechError && (
              <div style={{ color: 'red', marginTop: '10px', padding: '8px', backgroundColor: '#fff2f0', border: '1px solid #ffccc7', borderRadius: '4px' }}>
                ⚠️ 语音识别错误: {speechError}
              </div>
            )}

            {microphonePermission === 'denied' && (
              <div style={{ color: 'red', marginTop: '10px', padding: '8px', backgroundColor: '#fff2f0', border: '1px solid #ffccc7', borderRadius: '4px' }}>
                ⚠️ 麦克风权限被拒绝，请在浏览器地址栏左侧点击锁图标，允许麦克风访问。
              </div>
            )}

            {microphonePermission === 'prompt' && (
              <div style={{ color: 'orange', marginTop: '10px', padding: '8px', backgroundColor: '#fffbe6', border: '1px solid #ffe58f', borderRadius: '4px' }}>
                📢 首次使用时浏览器会询问麦克风权限，请点击"允许"。
              </div>
            )}

            {microphonePermission === 'granted' && (
              <div style={{ color: 'green', marginTop: '10px', padding: '8px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '4px' }}>
                ✅ 麦克风权限已获取，可以开始语音交流。
              </div>
            )}
          </div>
        </Modal>
      </div>
    </div>
  );
};

export default EnhancedImmersiveChatPage;
