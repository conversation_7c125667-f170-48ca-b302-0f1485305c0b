#!/usr/bin/env python3
"""
测试API连通性的脚本
"""
import requests
import json
import time

def test_backend_direct():
    """直接测试后端API"""
    print("🔧 测试后端API直连...")
    
    # 1. 测试根路径
    try:
        response = requests.get('http://127.0.0.1:8000/')
        print(f"✅ 根路径响应: {response.status_code}")
        if response.status_code == 200:
            print(f"📄 响应内容: {response.json()}")
    except Exception as e:
        print(f"❌ 根路径测试失败: {e}")
    
    # 2. 测试登录API
    try:
        login_data = {
            "username": "api_test_user",
            "password": "test_password_123"
        }
        response = requests.post('http://127.0.0.1:8000/api/auth/login/', json=login_data)
        print(f"🔐 登录API响应: {response.status_code}")
        
        if response.status_code == 200:
            token = response.json().get('token')
            print(f"✅ 获取到token: {token[:20]}...")
            return token
        else:
            print(f"❌ 登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 登录API测试失败: {e}")
        return None

def test_chat_api(token):
    """测试聊天API"""
    if not token:
        print("⚠️ 没有token，跳过聊天API测试")
        return
    
    print("\n💬 测试聊天API...")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 1. 测试通用聊天API
    try:
        chat_data = {
            "messages": [
                {"role": "user", "content": "你好，请简单介绍一下你自己"}
            ],
            "model": "4.0Ultra",
            "stream": False
        }
        
        response = requests.post(
            'http://127.0.0.1:8000/api/chat/spark/',
            json=chat_data,
            headers=headers,
            timeout=30
        )
        
        print(f"🌟 通用聊天API响应: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ AI回复: {result.get('choices', [{}])[0].get('message', {}).get('content', 'N/A')[:100]}...")
        else:
            print(f"❌ 通用聊天API失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 通用聊天API测试失败: {e}")
    
    # 2. 测试角色聊天API
    try:
        character_data = {
            "user_message": "你好，请简单介绍一下你自己",
            "enable_tts": False,
            "voice_mode": False
        }
        
        response = requests.post(
            'http://127.0.0.1:8000/api/characters/1/chat/',
            json=character_data,
            headers=headers,
            timeout=30
        )
        
        print(f"👤 角色聊天API响应: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 角色回复: {result.get('character_response', 'N/A')[:100]}...")
        else:
            print(f"❌ 角色聊天API失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 角色聊天API测试失败: {e}")

def test_frontend_proxy():
    """测试前端代理"""
    print("\n🌐 测试前端代理...")
    
    # 测试前端代理是否工作
    frontend_ports = [5173, 5174, 5175]
    
    for port in frontend_ports:
        try:
            response = requests.get(f'http://localhost:{port}/', timeout=5)
            if response.status_code == 200:
                print(f"✅ 前端服务器运行在端口 {port}")
                
                # 测试代理API
                try:
                    proxy_response = requests.get(f'http://localhost:{port}/api/', timeout=5)
                    print(f"🔄 代理API测试 (端口{port}): {proxy_response.status_code}")
                    if proxy_response.status_code == 200:
                        print(f"✅ 代理工作正常")
                    else:
                        print(f"⚠️ 代理可能有问题: {proxy_response.text[:100]}")
                except Exception as e:
                    print(f"❌ 代理API测试失败: {e}")
                
                break
        except Exception as e:
            print(f"⚠️ 端口 {port} 不可用: {e}")

def test_spark_service():
    """测试星火AI服务"""
    print("\n🌟 测试星火AI服务...")
    
    try:
        # 导入星火服务
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'backend-services'))
        
        from services.spark_http_service import spark_http_service
        
        if spark_http_service:
            print("✅ 星火HTTP服务已初始化")
            
            # 测试连接
            test_result = spark_http_service.test_connection()
            if test_result:
                print("✅ 星火AI连接测试成功")
            else:
                print("❌ 星火AI连接测试失败")
                
            # 测试对话
            response = spark_http_service.get_dialogue_response(
                character_prompt="你是一个友善的AI助手。",
                user_message="你好，请简单介绍一下你自己。"
            )
            
            if response and len(response) > 10:
                print(f"✅ 星火AI对话测试成功: {response[:100]}...")
            else:
                print(f"❌ 星火AI对话测试失败: {response}")
                
        else:
            print("❌ 星火HTTP服务未初始化")
            
    except Exception as e:
        print(f"❌ 星火AI服务测试失败: {e}")

def main():
    """主测试函数"""
    print("🧪 API连通性测试")
    print("=" * 50)
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 测试后端直连
    token = test_backend_direct()
    
    # 2. 测试聊天API
    test_chat_api(token)
    
    # 3. 测试前端代理
    test_frontend_proxy()
    
    # 4. 测试星火AI服务
    test_spark_service()
    
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    print("✅ 如果所有测试都通过，说明API连通性正常")
    print("❌ 如果有测试失败，请检查对应的服务配置")
    print("\n🔧 修复建议:")
    print("1. 确保Django服务器运行在8000端口")
    print("2. 确保前端服务器运行并配置了正确的代理")
    print("3. 检查星火AI的环境变量配置")
    print("4. 验证用户认证和权限设置")

if __name__ == "__main__":
    main()
