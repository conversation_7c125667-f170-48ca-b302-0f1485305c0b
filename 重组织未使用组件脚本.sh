#!/bin/bash

echo "📁 开始重组织未使用的组件..."
echo

# 设置项目根目录
PROJECT_ROOT="$(cd "$(dirname "$0")/virtual-character-platform-frontend" && pwd)"
cd "$PROJECT_ROOT"

echo "📁 当前工作目录: $(pwd)"
echo

# 创建存档目录结构
echo "🏗️ 创建存档目录结构..."
mkdir -p src/components-archive/{experimental,legacy,templates,third-party}
mkdir -p src/components-library
echo "✅ 目录结构创建完成"

# 创建备份
BACKUP_DIR="backup_reorganize_$(date +%Y%m%d_%H%M%S)"
echo "💾 创建备份目录: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"
cp -r src/components "$BACKUP_DIR/"

echo
echo "📦 开始移动组件..."

# 实验性组件
echo "🧪 移动实验性组件..."
experimental_components=(
    "HolographicCard"
    "RomanceCarousel"
    "DanceInfo"
    "VRMModelCard"
)

for component in "${experimental_components[@]}"; do
    if [ -d "src/components/$component" ]; then
        echo "  📦 移动 $component 到 experimental/"
        mv "src/components/$component" "src/components-archive/experimental/"
    fi
done

# 遗留组件
echo "📜 移动遗留组件..."
legacy_components=(
    "ChatItem_Legacy"
    "Error"
    "Menu"
)

for component in "${legacy_components[@]}"; do
    if [ -d "src/components/$component" ]; then
        echo "  📦 移动 $component 到 legacy/"
        mv "src/components/$component" "src/components-archive/legacy/"
    fi
done

# 模板组件
echo "📋 移动模板组件..."
template_components=(
    "Analytics"
    "BrandWatermark"
    "Branding"
    "Logo"
    "TopBanner"
)

for component in "${template_components[@]}"; do
    if [ -d "src/components/$component" ]; then
        echo "  📦 移动 $component 到 templates/"
        mv "src/components/$component" "src/components-archive/templates/"
    fi
done

# 第三方组件
echo "🔌 移动第三方组件..."
third_party_components=(
    "Application"
    "NProgress"
    "server"
)

for component in "${third_party_components[@]}"; do
    if [ -d "src/components/$component" ]; then
        echo "  📦 移动 $component 到 third-party/"
        mv "src/components/$component" "src/components-archive/third-party/"
    fi
done

# 可复用组件库
echo "📚 移动可复用组件..."
library_components=(
    "ModelIcon"
    "ModelSelect"
    "PanelTitle"
    "RoleCard"
    "TextArea"
)

for component in "${library_components[@]}"; do
    if [ -d "src/components/$component" ]; then
        echo "  📦 移动 $component 到 library/"
        mv "src/components/$component" "src/components-library/"
    fi
done

# 移动单个文件组件
echo "📄 移动单个文件组件..."
if [ -f "src/components/VoiceSelector.tsx" ]; then
    echo "  📦 移动 VoiceSelector.tsx 到 library/"
    mv "src/components/VoiceSelector.tsx" "src/components-library/"
fi

# 创建README文件
echo "📝 创建说明文档..."

cat > "src/components-archive/README.md" << 'EOF'
# 组件存档目录

本目录存放暂时未使用但可能在将来需要的组件。

## 目录结构

### experimental/
实验性和特效组件，包含创新功能和视觉效果：
- `HolographicCard/` - 全息卡片特效组件
- `RomanceCarousel/` - 浪漫主题轮播组件
- `DanceInfo/` - 舞蹈信息展示组件
- `VRMModelCard/` - VRM模型卡片组件

### legacy/
遗留组件，已被新版本替代但保留以备参考：
- `ChatItem_Legacy/` - 旧版聊天项组件
- `Error/` - 旧版错误处理组件
- `Menu/` - 旧版菜单组件

### templates/
模板和示例组件，可作为开发新功能的参考：
- `Analytics/` - 数据分析组件模板
- `BrandWatermark/` - 品牌水印组件模板
- `Branding/` - 品牌展示组件模板
- `Logo/` - Logo展示组件模板
- `TopBanner/` - 顶部横幅组件模板

### third-party/
第三方库示例和集成组件：
- `Application/` - 应用程序框架组件
- `NProgress/` - 进度条组件示例
- `server/` - 服务器端渲染组件

## 使用说明

1. **查找组件**: 如需使用某个组件，请先在此目录中查找
2. **移动组件**: 确认使用后，将组件移回 `src/components/` 目录
3. **更新导入**: 移动组件后记得更新相关的导入路径
4. **测试验证**: 使用前请进行充分的测试

## 维护建议

- 定期审查存档组件的必要性
- 删除确认不再需要的组件
- 更新组件文档和使用说明
EOF

cat > "src/components-library/README.md" << 'EOF'
# 可复用组件库

本目录存放通用的、可复用的组件，这些组件设计良好，可以在多个场景中使用。

## 组件列表

- `ModelIcon/` - 模型图标组件，支持多种模型类型
- `ModelSelect/` - 模型选择器组件，提供下拉选择功能
- `PanelTitle/` - 面板标题组件，统一的标题样式
- `RoleCard/` - 角色卡片组件，展示角色信息
- `TextArea/` - 增强的文本区域组件
- `VoiceSelector.tsx` - 语音选择器组件

## 使用方式

```typescript
// 从组件库导入
import { ModelIcon } from '@/components-library/ModelIcon';
import { RoleCard } from '@/components-library/RoleCard';
```

## 开发规范

1. **组件设计**: 保持组件的通用性和可复用性
2. **API设计**: 提供清晰、一致的props接口
3. **文档完善**: 每个组件都应有完整的使用文档
4. **类型安全**: 使用TypeScript确保类型安全
EOF

echo
echo "🎉 组件重组织完成！"
echo
echo "📊 重组织总结:"
echo "================"
echo "💾 备份目录: $BACKUP_DIR"
echo "📁 新的目录结构:"
echo "  📂 src/components-archive/"
echo "    ├── 🧪 experimental/ (实验性组件)"
echo "    ├── 📜 legacy/ (遗留组件)"
echo "    ├── 📋 templates/ (模板组件)"
echo "    └── 🔌 third-party/ (第三方组件)"
echo "  📂 src/components-library/ (可复用组件库)"
echo

echo "🧪 运行构建测试..."
if npm run build; then
    echo "✅ 构建测试通过！"
else
    echo "⚠️ 构建测试失败，可能需要更新导入路径"
fi

echo
echo "💡 后续建议:"
echo "============"
echo "1. 检查是否有代码引用了移动的组件"
echo "2. 更新相关的导入路径"
echo "3. 考虑在 tsconfig.json 中添加路径映射:"
echo "   \"@/components-archive/*\": [\"src/components-archive/*\"]"
echo "   \"@/components-library/*\": [\"src/components-library/*\"]"
echo "4. 更新项目文档，说明新的组件组织结构"
echo
echo "✅ 重组织完成！组件现在更有序了。"
