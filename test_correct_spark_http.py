#!/usr/bin/env python3
"""
正确的星火AI HTTP实现测试脚本
基于官方HTTP示例验证实现
"""

import requests
import json
import time
import sys
import os
from datetime import datetime

def test_official_spark_http_example():
    """测试官方星火AI HTTP示例"""
    print("🌟 测试官方星火AI HTTP示例...")
    
    # 检查环境变量
    api_password = os.getenv('SPARK_API_PASSWORD')
    if not api_password:
        print("❌ 环境变量 SPARK_API_PASSWORD 未配置")
        print("💡 请在 .env 文件中配置: SPARK_API_PASSWORD=your_api_password")
        print("📍 获取地址: https://console.xfyun.cn/services/bmx1")
        return False
    
    # 官方示例配置
    url = "https://spark-api-open.xf-yun.com/v1/chat/completions"
    headers = {
        'Authorization': f'Bearer {api_password}',
        'Content-Type': 'application/json'
    }
    
    # 测试请求体（基于官方示例）
    body = {
        "model": "4.0Ultra",
        "user": "test_user_id",
        "messages": [
            {
                "role": "system",
                "content": "你是一个友善的AI助手。"
            },
            {
                "role": "user",
                "content": "你好，请简单介绍一下你自己。"
            }
        ],
        "stream": False,  # 不使用流式响应
        "max_tokens": 1024,
        "temperature": 0.7
    }
    
    try:
        print(f"📡 发送请求到: {url}")
        print(f"🔑 使用API密码: {api_password[:10]}...")
        
        response = requests.post(
            url=url,
            json=body,
            headers=headers,
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功！")
            print(f"📝 响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            # 解析响应
            if 'choices' in result and len(result['choices']) > 0:
                choice = result['choices'][0]
                if 'message' in choice and 'content' in choice['message']:
                    ai_response = choice['message']['content']
                    print(f"🤖 AI响应: {ai_response}")
                    return True
                else:
                    print("❌ 响应格式错误：缺少message.content")
                    return False
            else:
                print("❌ 响应格式错误：缺少choices")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"📄 错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_our_spark_http_service():
    """测试我们的星火AI HTTP服务实现"""
    print("\n🔧 测试我们的星火AI HTTP服务实现...")
    
    try:
        # 导入我们的服务
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'backend-services'))
        
        from services.spark_http_service import spark_http_service
        
        if spark_http_service is None:
            print("❌ 星火HTTP服务初始化失败")
            return False
            
        print("✅ 星火HTTP服务初始化成功")
        
        # 测试连接
        connection_ok = spark_http_service.test_connection()
        if connection_ok:
            print("✅ 星火HTTP连接测试成功")
        else:
            print("⚠️ 星火HTTP连接测试失败")
            return False
        
        # 测试角色对话
        test_cases = [
            {
                "character_prompt": "你是神里绫华，一个温柔优雅的角色。请用绫华的语气回答。",
                "user_message": "你好，绫华！今天天气很好呢。",
                "description": "角色对话测试"
            },
            {
                "character_prompt": "你是可莉，一个活泼可爱的小女孩。请用可莉的语气回答。",
                "user_message": "可莉，我们一起去炸鱼吧！",
                "description": "角色个性测试"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 测试 {i}: {test_case['description']}")
            
            response = spark_http_service.get_dialogue_response(
                character_prompt=test_case['character_prompt'],
                user_message=test_case['user_message']
            )
            
            if response:
                print(f"✅ 响应成功: {response[:150]}...")
                if "抱歉" not in response and len(response) > 20:
                    print("✅ 响应质量良好")
                else:
                    print("⚠️ 响应质量一般")
            else:
                print("❌ 响应失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 服务测试失败: {e}")
        return False

def test_backend_api_integration():
    """测试后端API集成"""
    print("\n🔗 测试后端API集成...")
    
    base_url = "http://localhost:8000"
    
    # 测试角色聊天API
    test_cases = [
        {
            "character_id": "1",
            "message": "你好，我想了解一下你的性格特点。",
            "description": "角色1对话测试"
        },
        {
            "character_id": "2", 
            "message": "今天天气很好，你觉得呢？",
            "description": "角色2对话测试"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📝 {test_case['description']}")
        
        try:
            chat_url = f"{base_url}/api/characters/{test_case['character_id']}/chat/"
            test_data = {
                "user_message": test_case['message'],
                "enable_tts": False,
                "voice_mode": False
            }
            
            response = requests.post(
                chat_url,
                json=test_data,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API调用成功")
                
                if 'character_response' in data:
                    char_response = data['character_response']
                    print(f"🤖 AI响应: {char_response[:150]}...")
                    
                    # 检查响应质量
                    if "抱歉" not in char_response and len(char_response) > 10:
                        print("✅ 响应质量良好")
                    else:
                        print("⚠️ 响应质量一般")
                else:
                    print("⚠️ 响应格式异常")
                
                return True
            elif response.status_code == 401:
                print("⚠️ 需要认证，但API端点存在")
                return True
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"📄 响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    return True

def show_configuration_guide():
    """显示配置指南"""
    print("\n" + "="*60)
    print("📋 星火AI HTTP配置指南")
    print("="*60)
    
    print("\n🔑 获取API密码:")
    print("1. 访问: https://console.xfyun.cn/services/bmx1")
    print("2. 登录讯飞开放平台账号")
    print("3. 找到 'HTTP服务接口认证信息' 中的 'APIPassword'")
    print("4. 复制APIPassword值")
    
    print("\n⚙️ 配置环境变量:")
    print("在 .env 文件中添加:")
    print("SPARK_API_PASSWORD=your_api_password_here")
    
    print("\n🔄 与WebSocket版本的区别:")
    print("- WebSocket: 需要 APP_ID, API_KEY, API_SECRET")
    print("- HTTP: 只需要 APIPassword")
    print("- HTTP版本更适合角色agent设计")
    
    print("\n📡 API端点信息:")
    print("- URL: https://spark-api-open.xf-yun.com/v1/chat/completions")
    print("- 模型: 4.0Ultra")
    print("- 认证: Bearer {APIPassword}")

def main():
    """主测试函数"""
    print("🚀 正确的星火AI HTTP实现验证")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # 显示配置指南
    show_configuration_guide()
    
    tests = [
        ("官方星火AI HTTP示例", test_official_spark_http_example),
        ("我们的星火AI HTTP服务", test_our_spark_http_service),
        ("后端API集成", test_backend_api_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed >= total * 0.8:
        print("🎉 正确的星火AI HTTP实现验证成功！")
        print("\n💡 实现特点:")
        print("- ✅ 基于官方HTTP示例")
        print("- ✅ 使用正确的认证方式 (Bearer APIPassword)")
        print("- ✅ 适合角色agent设计")
        print("- ✅ 支持完整文字的情感分析")
    else:
        print("❌ 实现存在问题，请检查配置")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
