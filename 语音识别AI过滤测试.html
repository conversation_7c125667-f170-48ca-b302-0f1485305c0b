<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音识别AI过滤测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .test-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .ai-button {
            background: #52c41a;
            color: white;
        }
        .ai-button:hover {
            background: #73d13d;
        }
        .status-display {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status-listening {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .status-ai-speaking {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .status-idle {
            background: #f6f6f6;
            border: 1px solid #d9d9d9;
            color: #666;
        }
        .log-area {
            background: #f6f6f6;
            border: 1px solid #d9d9d9;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .ai-response-display {
            background: #fff2e8;
            border: 1px solid #ffbb96;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            min-height: 50px;
        }
        .transcript-display {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            min-height: 50px;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .warning { color: #faad14; }
        .info { color: #1890ff; }
        .filtered { color: #ff4d4f; font-weight: bold; }
        .control-panel {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <h1>🎤🚫 语音识别AI过滤测试</h1>
    
    <div class="test-section">
        <h2>📋 问题描述</h2>
        <p><strong>原问题</strong>: 语音识别会把AI播放的语音误识别为用户输入，造成无限循环</p>
        <p><strong>修复方案</strong>: 
            <br>1. <strong>智能暂停</strong> - AI播放时自动暂停语音识别
            <br>2. <strong>内容过滤</strong> - 识别并过滤AI语音内容
            <br>3. <strong>相似度检测</strong> - 计算文本相似度，过滤高相似内容
        </p>
    </div>

    <div class="test-section">
        <h2>🧪 AI语音过滤测试</h2>
        
        <div class="status-display" id="status-display">
            状态: 待机中
        </div>
        
        <div class="control-panel">
            <button class="test-button" id="start-btn" onclick="startRecognition()">开始语音识别</button>
            <button class="test-button" id="stop-btn" onclick="stopRecognition()" disabled>停止识别</button>
            <button class="ai-button" id="simulate-ai-btn" onclick="simulateAISpeech()">模拟AI播放</button>
            <button class="test-button" onclick="clearLog()">清除日志</button>
        </div>
        
        <div class="ai-response-display" id="ai-response-display">
            <strong>最近AI回复:</strong> <span id="ai-response-text">暂无</span>
        </div>
        
        <div class="transcript-display" id="transcript-display">
            <strong>识别结果:</strong> <span id="transcript-text">等待语音输入...</span>
        </div>
        
        <div>
            <strong>过滤统计:</strong> 
            总识别: <span id="total-count">0</span> | 
            已过滤: <span id="filtered-count">0</span> | 
            通过: <span id="passed-count">0</span>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 测试日志</h2>
        <div id="log-area" class="log-area"></div>
    </div>

    <div class="test-section">
        <h2>💡 测试场景</h2>
        <ol>
            <li><strong>正常对话测试</strong>: 开始识别后，正常说话，观察识别结果</li>
            <li><strong>AI播放测试</strong>: 点击"模拟AI播放"，观察识别是否暂停</li>
            <li><strong>内容过滤测试</strong>: AI播放后，重复说AI刚才的内容，观察是否被过滤</li>
            <li><strong>相似度测试</strong>: 说与AI回复相似但不完全相同的内容</li>
        </ol>
    </div>

    <script>
        let recognition = null;
        let isListening = false;
        let isAISpeaking = false;
        let wasListeningBeforeAI = false;
        let recentAIResponses = [];
        let totalCount = 0;
        let filteredCount = 0;
        let passedCount = 0;

        // 模拟AI回复内容
        const aiResponses = [
            "今天天气很好，适合出门散步。",
            "我是你的AI助手，有什么可以帮助你的吗？",
            "根据你的问题，我建议你可以尝试以下方法。",
            "感谢你的提问，让我来为你详细解答。",
            "这是一个很有趣的话题，我们来深入讨论一下。"
        ];

        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logArea.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log-area').innerHTML = '';
            totalCount = 0;
            filteredCount = 0;
            passedCount = 0;
            updateStats();
        }

        function updateStatus(status, type = 'idle') {
            const statusDisplay = document.getElementById('status-display');
            statusDisplay.textContent = `状态: ${status}`;
            statusDisplay.className = `status-display status-${type}`;
        }

        function updateStats() {
            document.getElementById('total-count').textContent = totalCount;
            document.getElementById('filtered-count').textContent = filteredCount;
            document.getElementById('passed-count').textContent = passedCount;
        }

        function updateAIResponse(response) {
            document.getElementById('ai-response-text').textContent = response;
            recentAIResponses.unshift(response);
            if (recentAIResponses.length > 3) {
                recentAIResponses = recentAIResponses.slice(0, 3);
            }
            log(`📝 更新AI回复历史: ${response}`, 'info');
        }

        function updateTranscript(transcript, isFiltered = false) {
            const transcriptText = document.getElementById('transcript-text');
            if (isFiltered) {
                transcriptText.innerHTML = `<span style="color: #ff4d4f; text-decoration: line-through;">${transcript}</span> (已过滤)`;
            } else {
                transcriptText.textContent = transcript;
            }
        }

        // 计算文本相似度
        function calculateSimilarity(text1, text2) {
            const chars1 = text1.split('');
            const chars2 = text2.split('');
            const intersection = chars1.filter(char => chars2.includes(char));
            return intersection.length / Math.max(chars1.length, chars2.length);
        }

        // 检查是否为AI内容
        function isAIContent(transcript) {
            const cleanTranscript = transcript.trim().toLowerCase();
            
            for (const aiResponse of recentAIResponses) {
                const cleanAIResponse = aiResponse.toLowerCase();
                
                // 检查包含关系
                if (cleanTranscript.includes(cleanAIResponse.substring(0, 10)) || 
                    cleanAIResponse.includes(cleanTranscript.substring(0, 10))) {
                    log(`🚫 检测到AI语音内容(包含检查): ${transcript}`, 'filtered');
                    return true;
                }
                
                // 检查相似度
                const similarity = calculateSimilarity(cleanTranscript, cleanAIResponse);
                if (similarity > 0.7) {
                    log(`🚫 检测到高相似度AI内容: ${transcript} (相似度: ${similarity.toFixed(2)})`, 'filtered');
                    return true;
                }
            }
            
            return false;
        }

        function initRecognition() {
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                log('浏览器不支持语音识别', 'error');
                return false;
            }

            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            recognition = new SpeechRecognition();

            recognition.continuous = true;
            recognition.interimResults = true;
            recognition.lang = 'zh-CN';
            recognition.maxAlternatives = 1;

            recognition.onstart = function() {
                log('语音识别开始', 'success');
                isListening = true;
                updateStatus('正在监听...', 'listening');
                
                document.getElementById('start-btn').disabled = true;
                document.getElementById('stop-btn').disabled = false;
            };

            recognition.onresult = function(event) {
                let finalTranscript = '';

                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    if (event.results[i].isFinal) {
                        finalTranscript += transcript;
                    }
                }

                if (finalTranscript.trim()) {
                    totalCount++;
                    log(`收到语音识别结果: ${finalTranscript}`, 'info');
                    
                    // 检查是否为AI内容
                    if (isAIContent(finalTranscript)) {
                        filteredCount++;
                        updateTranscript(finalTranscript, true);
                        log(`🚫 AI内容已过滤: ${finalTranscript}`, 'warning');
                    } else {
                        passedCount++;
                        updateTranscript(finalTranscript, false);
                        log(`✅ 用户语音通过: ${finalTranscript}`, 'success');
                    }
                    
                    updateStats();
                }
            };

            recognition.onend = function() {
                log('语音识别结束', 'warning');
                isListening = false;
                
                // 如果AI正在说话，不要重启识别
                if (!isAISpeaking) {
                    updateStatus('识别完成', 'idle');
                    document.getElementById('start-btn').disabled = false;
                    document.getElementById('stop-btn').disabled = true;
                }
            };

            recognition.onerror = function(event) {
                log(`识别错误: ${event.error}`, 'error');
            };

            return true;
        }

        function startRecognition() {
            if (!recognition && !initRecognition()) {
                return;
            }

            log('开始语音识别测试', 'info');
            
            try {
                recognition.start();
            } catch (error) {
                log(`启动失败: ${error.message}`, 'error');
            }
        }

        function stopRecognition() {
            if (recognition && isListening) {
                recognition.stop();
                log('手动停止识别', 'info');
            }
            
            updateStatus('已停止', 'idle');
            document.getElementById('start-btn').disabled = false;
            document.getElementById('stop-btn').disabled = true;
        }

        function simulateAISpeech() {
            const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];
            
            log('🤖 开始模拟AI播放', 'info');
            updateAIResponse(randomResponse);
            
            // 模拟AI开始说话
            isAISpeaking = true;
            updateStatus('AI正在说话...', 'ai-speaking');
            
            // 如果正在识别，暂停识别
            if (isListening) {
                log('🔇 AI开始说话，暂停语音识别', 'warning');
                wasListeningBeforeAI = true;
                recognition.stop();
            }
            
            // 模拟AI说话时间（3-5秒）
            const speakDuration = 3000 + Math.random() * 2000;
            
            setTimeout(() => {
                log('🤖 AI播放完成', 'info');
                isAISpeaking = false;
                
                // 如果之前在识别，恢复识别
                if (wasListeningBeforeAI) {
                    log('🎤 AI说话结束，恢复语音识别', 'success');
                    setTimeout(() => {
                        if (recognition) {
                            recognition.start();
                            wasListeningBeforeAI = false;
                        }
                    }, 500);
                } else {
                    updateStatus('待机中', 'idle');
                }
            }, speakDuration);
        }

        // 页面加载完成后初始化
        window.onload = function() {
            log('页面加载完成，准备测试AI语音过滤', 'info');
            updateStatus('待机中', 'idle');
            updateStats();
        };
    </script>
</body>
</html>
