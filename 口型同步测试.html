<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>口型同步功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .viseme-display {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        .viseme-item {
            padding: 5px 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            background-color: white;
            transition: all 0.3s ease;
        }
        .viseme-item.active {
            border-color: #007bff;
            background-color: #e7f3ff;
            font-weight: bold;
        }
        .audio-controls {
            margin: 15px 0;
        }
        .audio-controls input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .metric {
            padding: 10px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-align: center;
        }
        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 口型同步功能测试</h1>
        
        <div class="test-section">
            <h3>📋 测试说明</h3>
            <p>本页面用于测试VRM模型的口型同步功能，验证音频分析和口型动画的同步效果。</p>
            <div class="status info">
                <strong>测试目标：</strong>
                <ul>
                    <li>验证音频特征提取功能</li>
                    <li>测试口型分析算法</li>
                    <li>检查VRM动画参数应用</li>
                    <li>确认同步时间精度</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🎵 音频测试</h3>
            <div class="audio-controls">
                <button onclick="startMicrophoneTest()">开始麦克风测试</button>
                <button onclick="stopMicrophoneTest()">停止测试</button>
                <button onclick="playTestAudio()">播放测试音频</button>
                <button onclick="generateToneTest()">生成测试音调</button>
            </div>
            
            <div>
                <label>测试频率: <span id="frequencyValue">440</span> Hz</label>
                <input type="range" id="frequencySlider" min="100" max="2000" value="440" 
                       oninput="updateFrequency(this.value)">
            </div>
            
            <div class="status" id="audioStatus">等待开始测试...</div>
        </div>

        <div class="test-section">
            <h3>👄 口型分析</h3>
            <div class="viseme-display">
                <div class="viseme-item" data-viseme="aa">AA (啊)</div>
                <div class="viseme-item" data-viseme="ih">IH (伊)</div>
                <div class="viseme-item" data-viseme="ou">OU (欧)</div>
                <div class="viseme-item" data-viseme="ee">EE (衣)</div>
                <div class="viseme-item" data-viseme="oh">OH (哦)</div>
                <div class="viseme-item" data-viseme="eh">EH (额)</div>
            </div>
            
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="volumeValue">0.00</div>
                    <div class="metric-label">音量</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="mouthOpennessValue">0.00</div>
                    <div class="metric-label">嘴部开合</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="intensityValue">0.00</div>
                    <div class="metric-label">音频强度</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="currentViseme">--</div>
                    <div class="metric-label">当前口型</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 实时数据</h3>
            <div class="log" id="logOutput">等待测试数据...</div>
            <button onclick="clearLog()">清除日志</button>
            <button onclick="exportTestData()">导出测试数据</button>
        </div>

        <div class="test-section">
            <h3>🔧 调试工具</h3>
            <button onclick="testVRMConnection()">测试VRM连接</button>
            <button onclick="testAudioContext()">测试音频上下文</button>
            <button onclick="simulateVisemes()">模拟口型序列</button>
            <button onclick="benchmarkPerformance()">性能基准测试</button>
        </div>
    </div>

    <script>
        // 全局变量
        let audioContext = null;
        let analyser = null;
        let microphone = null;
        let isRecording = false;
        let animationFrame = null;
        let testData = [];
        let oscillator = null;

        // 初始化音频上下文
        async function initAudioContext() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                analyser = audioContext.createAnalyser();
                analyser.fftSize = 2048;
                analyser.smoothingTimeConstant = 0.8;
                
                log('✅ 音频上下文初始化成功', 'success');
                return true;
            } catch (error) {
                log('❌ 音频上下文初始化失败: ' + error.message, 'error');
                return false;
            }
        }

        // 开始麦克风测试
        async function startMicrophoneTest() {
            if (isRecording) return;
            
            if (!audioContext && !(await initAudioContext())) {
                return;
            }

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                microphone = audioContext.createMediaStreamSource(stream);
                microphone.connect(analyser);
                
                isRecording = true;
                updateStatus('🎤 正在录音...', 'info');
                log('🎤 麦克风测试开始', 'info');
                
                startAnalysis();
            } catch (error) {
                log('❌ 麦克风访问失败: ' + error.message, 'error');
                updateStatus('麦克风访问失败', 'error');
            }
        }

        // 停止麦克风测试
        function stopMicrophoneTest() {
            if (!isRecording) return;
            
            isRecording = false;
            if (animationFrame) {
                cancelAnimationFrame(animationFrame);
            }
            
            if (microphone) {
                microphone.disconnect();
                microphone = null;
            }
            
            updateStatus('测试已停止', 'info');
            log('⏹️ 麦克风测试停止', 'info');
        }

        // 开始音频分析
        function startAnalysis() {
            if (!isRecording || !analyser) return;
            
            const bufferLength = analyser.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);
            const timeDomainData = new Float32Array(analyser.fftSize);
            
            function analyze() {
                if (!isRecording) return;
                
                analyser.getByteFrequencyData(dataArray);
                analyser.getFloatTimeDomainData(timeDomainData);
                
                // 计算音量
                let volume = 0;
                for (let i = 0; i < timeDomainData.length; i++) {
                    volume = Math.max(volume, Math.abs(timeDomainData[i]));
                }
                volume = 1 / (1 + Math.exp(-45 * volume + 5));
                if (volume < 0.1) volume = 0;
                
                // 分析频率特征
                const visemeData = analyzeViseme(dataArray);
                const mouthOpenness = Math.min(volume * 1.2, 1.0);
                
                // 更新显示
                updateMetrics(volume, mouthOpenness, visemeData.intensity, visemeData.viseme);
                updateVisemeDisplay(visemeData.viseme);
                
                // 记录测试数据
                const timestamp = Date.now();
                testData.push({
                    timestamp,
                    volume,
                    mouthOpenness,
                    viseme: visemeData.viseme,
                    intensity: visemeData.intensity
                });
                
                // 限制数据量
                if (testData.length > 1000) {
                    testData.shift();
                }
                
                animationFrame = requestAnimationFrame(analyze);
            }
            
            analyze();
        }

        // 分析口型
        function analyzeViseme(frequencyData) {
            const lowFreq = getFrequencyRangeEnergy(frequencyData, 0, 300);
            const midFreq = getFrequencyRangeEnergy(frequencyData, 300, 2000);
            const highFreq = getFrequencyRangeEnergy(frequencyData, 2000, 8000);
            
            const totalEnergy = lowFreq + midFreq + highFreq;
            if (totalEnergy < 0.1) {
                return { viseme: 'aa', intensity: 0 };
            }
            
            let viseme = 'aa';
            if (highFreq > midFreq && highFreq > lowFreq) {
                viseme = Math.random() > 0.5 ? 'ee' : 'ih';
            } else if (midFreq > lowFreq) {
                viseme = Math.random() > 0.5 ? 'eh' : 'ih';
            } else {
                viseme = Math.random() > 0.5 ? 'aa' : 'oh';
            }
            
            return { viseme, intensity: Math.min(totalEnergy * 2, 1.0) };
        }

        // 获取频率范围能量
        function getFrequencyRangeEnergy(frequencyData, minFreq, maxFreq) {
            const nyquist = audioContext.sampleRate / 2;
            const minBin = Math.floor((minFreq / nyquist) * frequencyData.length);
            const maxBin = Math.floor((maxFreq / nyquist) * frequencyData.length);
            
            let energy = 0;
            for (let i = minBin; i < maxBin && i < frequencyData.length; i++) {
                energy += frequencyData[i] / 255.0;
            }
            
            return energy / (maxBin - minBin);
        }

        // 更新指标显示
        function updateMetrics(volume, mouthOpenness, intensity, viseme) {
            document.getElementById('volumeValue').textContent = volume.toFixed(2);
            document.getElementById('mouthOpennessValue').textContent = mouthOpenness.toFixed(2);
            document.getElementById('intensityValue').textContent = intensity.toFixed(2);
            document.getElementById('currentViseme').textContent = viseme.toUpperCase();
        }

        // 更新口型显示
        function updateVisemeDisplay(activeViseme) {
            document.querySelectorAll('.viseme-item').forEach(item => {
                const viseme = item.getAttribute('data-viseme');
                if (viseme === activeViseme) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }

        // 更新状态
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('audioStatus');
            statusEl.textContent = message;
            statusEl.className = 'status ' + type;
        }

        // 记录日志
        function log(message, type = 'info') {
            const logEl = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logEl.textContent += logEntry;
            logEl.scrollTop = logEl.scrollHeight;
        }

        // 清除日志
        function clearLog() {
            document.getElementById('logOutput').textContent = '';
            testData = [];
        }

        // 更新频率
        function updateFrequency(value) {
            document.getElementById('frequencyValue').textContent = value;
        }

        // 生成测试音调
        async function generateToneTest() {
            if (!audioContext && !(await initAudioContext())) {
                return;
            }
            
            const frequency = parseInt(document.getElementById('frequencySlider').value);
            
            if (oscillator) {
                oscillator.stop();
            }
            
            oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 2);
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.start();
            oscillator.stop(audioContext.currentTime + 2);
            
            log(`🎵 生成 ${frequency}Hz 测试音调`, 'info');
        }

        // 测试VRM连接
        function testVRMConnection() {
            // 这里应该连接到实际的VRM系统
            log('🤖 VRM连接测试 - 模拟模式', 'info');
            log('✅ VRM模型加载状态: 正常', 'success');
            log('✅ 表情控制器: 可用', 'success');
            log('✅ 口型同步接口: 就绪', 'success');
        }

        // 测试音频上下文
        async function testAudioContext() {
            if (!audioContext) {
                await initAudioContext();
            }
            
            log('🔊 音频上下文状态: ' + audioContext.state, 'info');
            log('🔊 采样率: ' + audioContext.sampleRate + ' Hz', 'info');
            log('🔊 基础延迟: ' + (audioContext.baseLatency * 1000).toFixed(2) + ' ms', 'info');
        }

        // 模拟口型序列
        function simulateVisemes() {
            const visemes = ['aa', 'ih', 'ou', 'ee', 'oh', 'eh'];
            let index = 0;
            
            const interval = setInterval(() => {
                if (index >= visemes.length) {
                    clearInterval(interval);
                    updateVisemeDisplay('');
                    log('🎭 口型序列模拟完成', 'success');
                    return;
                }
                
                const viseme = visemes[index];
                updateVisemeDisplay(viseme);
                log(`🎭 模拟口型: ${viseme.toUpperCase()}`, 'info');
                index++;
            }, 500);
        }

        // 性能基准测试
        function benchmarkPerformance() {
            log('⚡ 开始性能基准测试...', 'info');
            
            const iterations = 10000;
            const testData = new Uint8Array(1024);
            for (let i = 0; i < testData.length; i++) {
                testData[i] = Math.random() * 255;
            }
            
            const startTime = performance.now();
            
            for (let i = 0; i < iterations; i++) {
                analyzeViseme(testData);
            }
            
            const endTime = performance.now();
            const avgTime = (endTime - startTime) / iterations;
            
            log(`⚡ 性能测试完成: ${iterations} 次迭代`, 'success');
            log(`⚡ 平均处理时间: ${avgTime.toFixed(4)} ms`, 'success');
            log(`⚡ 理论最大FPS: ${(1000 / avgTime).toFixed(0)}`, 'success');
        }

        // 导出测试数据
        function exportTestData() {
            if (testData.length === 0) {
                log('❌ 没有测试数据可导出', 'error');
                return;
            }
            
            const csvContent = 'timestamp,volume,mouthOpenness,viseme,intensity\n' +
                testData.map(row => `${row.timestamp},${row.volume},${row.mouthOpenness},${row.viseme},${row.intensity}`).join('\n');
            
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `lip_sync_test_${Date.now()}.csv`;
            a.click();
            window.URL.revokeObjectURL(url);
            
            log(`📊 测试数据已导出 (${testData.length} 条记录)`, 'success');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 口型同步测试页面已加载', 'success');
            log('💡 点击"开始麦克风测试"开始测试', 'info');
        });
    </script>
</body>
</html>
