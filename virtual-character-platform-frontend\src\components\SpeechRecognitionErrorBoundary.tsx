import React, { Component, ErrorInfo, ReactNode } from 'react';
import { message } from 'antd';

interface Props {
  children: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

/**
 * 语音识别错误边界组件
 * 
 * 功能：
 * - 捕获语音识别相关的JavaScript错误
 * - 防止InvalidStateError导致页面重新加载
 * - 提供错误恢复机制
 * - 记录详细的错误信息用于调试
 */
export class SpeechRecognitionErrorBoundary extends Component<Props, State> {
  private retryTimeout: NodeJS.Timeout | null = null;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 更新state以显示错误UI
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('🚨 SpeechRecognitionErrorBoundary 捕获到错误:', error);
    console.error('📍 错误详情:', errorInfo);

    this.setState({
      error,
      errorInfo,
    });

    // 调用外部错误处理器
    this.props.onError?.(error, errorInfo);

    // 特殊处理语音识别相关错误
    this.handleSpeechRecognitionError(error);
  }

  /**
   * 处理语音识别相关错误
   */
  private handleSpeechRecognitionError = (error: Error) => {
    const errorMessage = error.message || error.toString();
    
    // 检查是否是语音识别相关错误
    if (this.isSpeechRecognitionError(error)) {
      console.warn('🎤 检测到语音识别错误，尝试恢复...');
      
      // 显示用户友好的错误提示
      if (errorMessage.includes('InvalidStateError')) {
        message.warning('语音识别状态异常，正在自动恢复...', 3);
      } else if (errorMessage.includes('NotAllowedError')) {
        message.error('麦克风权限被拒绝，请检查浏览器设置', 5);
      } else if (errorMessage.includes('NotSupportedError')) {
        message.error('当前浏览器不支持语音识别功能', 5);
      } else {
        message.warning('语音识别遇到问题，正在尝试恢复...', 3);
      }

      // 尝试自动恢复
      this.attemptRecovery();
    } else {
      // 非语音识别错误，显示通用错误信息
      message.error('应用遇到错误，正在尝试恢复...', 3);
      this.attemptRecovery();
    }
  };

  /**
   * 判断是否是语音识别相关错误
   */
  private isSpeechRecognitionError = (error: Error): boolean => {
    const errorMessage = error.message || error.toString();
    const speechErrorKeywords = [
      'SpeechRecognition',
      'webkitSpeechRecognition',
      'InvalidStateError',
      'NotAllowedError',
      'NotSupportedError',
      'recognition',
      'microphone',
      'audio'
    ];

    return speechErrorKeywords.some(keyword => 
      errorMessage.toLowerCase().includes(keyword.toLowerCase())
    );
  };

  /**
   * 尝试错误恢复
   */
  private attemptRecovery = () => {
    const { retryCount } = this.state;

    if (retryCount < this.maxRetries) {
      console.log(`🔄 尝试错误恢复 (${retryCount + 1}/${this.maxRetries})`);
      
      // 清除之前的重试定时器
      if (this.retryTimeout) {
        clearTimeout(this.retryTimeout);
      }

      // 延迟恢复，给系统时间清理状态
      this.retryTimeout = setTimeout(() => {
        this.setState(prevState => ({
          hasError: false,
          error: null,
          errorInfo: null,
          retryCount: prevState.retryCount + 1,
        }));

        console.log('✅ 错误边界已重置，组件将重新渲染');
      }, 1000 + retryCount * 500); // 递增延迟
    } else {
      console.error('❌ 达到最大重试次数，停止自动恢复');
      message.error('应用遇到持续错误，请刷新页面', 10);
    }
  };

  /**
   * 手动重试
   */
  private handleManualRetry = () => {
    console.log('🔄 用户手动重试');
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    });
  };

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
  }

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误UI
      return (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          backgroundColor: '#fff2f0',
          border: '1px solid #ffccc7',
          borderRadius: '6px',
          margin: '10px'
        }}>
          <h3 style={{ color: '#cf1322', marginBottom: '10px' }}>
            🚨 语音功能遇到问题
          </h3>
          <p style={{ color: '#595959', marginBottom: '15px' }}>
            {this.state.retryCount < this.maxRetries 
              ? '正在自动恢复中，请稍候...' 
              : '自动恢复失败，请手动重试或刷新页面'
            }
          </p>
          
          {this.state.retryCount >= this.maxRetries && (
            <div>
              <button
                onClick={this.handleManualRetry}
                style={{
                  backgroundColor: '#1890ff',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginRight: '10px'
                }}
              >
                重试
              </button>
              <button
                onClick={() => window.location.reload()}
                style={{
                  backgroundColor: '#52c41a',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                刷新页面
              </button>
            </div>
          )}

          {/* 开发模式下显示详细错误信息 */}
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details style={{ marginTop: '15px', textAlign: 'left' }}>
              <summary style={{ cursor: 'pointer', color: '#1890ff' }}>
                查看错误详情 (开发模式)
              </summary>
              <pre style={{
                backgroundColor: '#f5f5f5',
                padding: '10px',
                borderRadius: '4px',
                fontSize: '12px',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                {this.state.error.toString()}
                {this.state.errorInfo?.componentStack}
              </pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default SpeechRecognitionErrorBoundary;
