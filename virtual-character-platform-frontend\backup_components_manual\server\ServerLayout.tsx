import { FC, PropsWithChildren } from 'react';

// 简单的移动设备检测
const isMobileDevice = () => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth <= 768;
};

interface ServerLayoutProps<T> {
  Desktop: FC<T>;
  Mobile: FC<T>;
}

const ServerLayout =
  <T extends PropsWithChildren>({ Desktop, Mobile }: ServerLayoutProps<T>): FC<T> =>
  (props: T) => {
    const mobile = isMobileDevice();
    return mobile ? <Mobile {...props} /> : <Desktop {...props} />;
  };

ServerLayout.displayName = 'ServerLayout';

export default ServerLayout;
