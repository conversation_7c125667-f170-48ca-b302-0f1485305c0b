#!/usr/bin/env python3
"""
测试/api/chat/spark 500错误的诊断脚本
"""

import requests
import json
import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'virtual_character_platform.settings')
django.setup()

def test_spark_api_direct():
    """直接测试/api/chat/spark端点"""
    print("=== 测试 /api/chat/spark 端点 ===")
    
    url = "http://127.0.0.1:8000/api/chat/spark"
    
    # 测试数据（模拟前端发送的请求）
    test_data = {
        "messages": [
            {
                "role": "system",
                "content": "你是一个情感分析专家，请分析用户输入的情感状态。"
            },
            {
                "role": "user", 
                "content": "你谁呀"
            }
        ],
        "model": "4.0Ultra",
        "stream": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-token"  # 需要认证
    }
    
    try:
        print(f"发送请求到: {url}")
        print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ API调用成功!")
            try:
                result = response.json()
                print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            except:
                print(f"响应文本: {response.text}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

def test_spark_service_directly():
    """直接测试星火AI服务"""
    print("\n=== 测试星火AI服务 ===")
    
    try:
        from services.spark_http_service import spark_http_service
        
        if spark_http_service is None:
            print("❌ 星火AI服务未初始化")
            return
            
        print("✅ 星火AI服务已初始化")
        print(f"服务URL: {spark_http_service.url}")
        print(f"模型: {spark_http_service.model}")
        
        # 测试连接
        print("测试连接...")
        test_result = spark_http_service.test_connection()
        
        if test_result:
            print("✅ 星火AI连接测试成功")
        else:
            print("❌ 星火AI连接测试失败")
            
        # 测试对话
        print("测试对话...")
        response = spark_http_service.get_dialogue_response(
            character_prompt="你是一个友善的AI助手。",
            user_message="你好，请简单介绍一下你自己。"
        )
        
        print(f"对话响应: {response}")
        
    except Exception as e:
        print(f"❌ 星火AI服务测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

def check_environment_variables():
    """检查环境变量配置"""
    print("\n=== 检查环境变量 ===")
    
    required_vars = [
        'SPARK_API_PASSWORD',
        'SPARK_APP_ID', 
        'SPARK_API_KEY',
        'SPARK_API_SECRET'
    ]
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * len(value[:4])}...")
        else:
            print(f"❌ {var}: 未设置")

def test_generic_chat_view():
    """测试GenericChatView的实现"""
    print("\n=== 测试 GenericChatView ===")
    
    try:
        from core.views import GenericChatView
        from django.test import RequestFactory
        from django.contrib.auth.models import User
        import json
        
        # 创建测试请求
        factory = RequestFactory()
        
        test_data = {
            "messages": [
                {"role": "system", "content": "你是一个情感分析专家"},
                {"role": "user", "content": "你谁呀"}
            ],
            "model": "4.0Ultra",
            "stream": False
        }
        
        request = factory.post(
            '/api/chat/spark',
            data=json.dumps(test_data),
            content_type='application/json'
        )
        
        # 创建测试用户
        try:
            user = User.objects.get(username='testuser')
        except User.DoesNotExist:
            user = User.objects.create_user('testuser', '<EMAIL>', 'testpass')
        
        request.user = user
        
        # 测试视图
        view = GenericChatView()
        response = view.post(request, provider='spark')
        
        print(f"视图响应状态: {response.status_code}")
        print(f"响应内容: {response.content.decode()}")
        
    except Exception as e:
        print(f"❌ GenericChatView测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始诊断 /api/chat/spark 500错误...")
    
    # 1. 检查环境变量
    check_environment_variables()
    
    # 2. 测试星火AI服务
    test_spark_service_directly()
    
    # 3. 测试GenericChatView
    test_generic_chat_view()
    
    # 4. 测试API端点
    test_spark_api_direct()
    
    print("\n诊断完成!")
