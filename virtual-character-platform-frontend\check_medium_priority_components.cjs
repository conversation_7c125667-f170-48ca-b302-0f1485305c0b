const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 中优先级组件列表
const MEDIUM_PRIORITY_COMPONENTS = [
  { name: 'ChatItem_Legacy', path: 'src/components/ChatItem_Legacy', type: 'directory' },
  { name: 'Error', path: 'src/components/Error', type: 'directory' },
  { name: 'Menu', path: 'src/components/Menu', type: 'directory' },
  { name: 'PanelTitle', path: 'src/components/PanelTitle', type: 'directory' },
  { name: 'RoleCard', path: 'src/components/RoleCard', type: 'directory' },
  { name: 'TextArea', path: 'src/components/TextArea', type: 'directory' },
  { name: 'server', path: 'src/components/server', type: 'directory' }
];

// 检查组件是否被引用
function checkComponentReferences(componentName) {
  console.log(`🔍 检查组件引用: ${componentName}`);
  
  const searchPatterns = [
    `import.*${componentName}`,
    `from.*${componentName}`,
    `'.*${componentName}'`,
    `".*${componentName}"`,
    `<${componentName}`,
    `${componentName}\\s*\\(`
  ];
  
  const references = [];
  
  for (const pattern of searchPatterns) {
    try {
      // 使用PowerShell的Select-String命令替代grep
      const result = execSync(`powershell -Command "Get-ChildItem -Path src\\ -Recurse -Include *.tsx,*.ts,*.js,*.jsx | Select-String -Pattern '${pattern}' | Select-Object -ExpandProperty Line"`, 
        { encoding: 'utf8' });
      
      if (result.trim()) {
        const lines = result.trim().split('\n');
        references.push(...lines);
      }
    } catch (error) {
      // 忽略错误，继续检查下一个模式
    }
  }
  
  // 去重并过滤掉自身文件的引用
  const uniqueReferences = [...new Set(references)]
    .filter(ref => ref.trim().length > 0)
    .filter(ref => !ref.includes(`components/${componentName}/`))
    .filter(ref => !ref.includes(`components\\${componentName}\\`));
  
  return {
    hasReferences: uniqueReferences.length > 0,
    references: uniqueReferences
  };
}

// 主函数
async function main() {
  console.log('🚀 开始检查中优先级组件引用情况...\n');
  
  const results = [];
  
  for (const component of MEDIUM_PRIORITY_COMPONENTS) {
    const result = checkComponentReferences(component.name);
    
    if (result.hasReferences) {
      console.log(`❌ ${component.name} 仍有引用:`);
      result.references.forEach(ref => console.log(`   ${ref}`));
      results.push({ component, canDelete: false, references: result.references });
    } else {
      console.log(`✅ ${component.name} 无引用，可以安全删除`);
      results.push({ component, canDelete: true, references: [] });
    }
    console.log('');
  }
  
  // 生成报告
  console.log('\n📊 中优先级组件检查报告');
  console.log('=' .repeat(50));
  
  const canDelete = results.filter(r => r.canDelete);
  const hasReferences = results.filter(r => !r.canDelete);
  
  console.log(`✅ 可以安全删除: ${canDelete.length} 个组件`);
  canDelete.forEach(r => console.log(`   - ${r.component.name}`));
  
  console.log(`\n❌ 仍有引用: ${hasReferences.length} 个组件`);
  hasReferences.forEach(r => console.log(`   - ${r.component.name}`));
  
  if (hasReferences.length > 0) {
    console.log('\n⚠️ 建议：先修复引用问题，再进行删除');
  } else {
    console.log('\n🎉 所有中优先级组件都可以安全删除！');
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  checkComponentReferences,
  MEDIUM_PRIORITY_COMPONENTS
};
