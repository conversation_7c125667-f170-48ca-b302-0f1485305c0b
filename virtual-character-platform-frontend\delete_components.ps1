# Windows PowerShell脚本：删除高优先级组件
# 在virtual-character-platform-frontend目录下运行

$componentsToDelete = @(
    @{name="BrandWatermark"; path="src\components\BrandWatermark"; type="directory"},
    @{name="Branding"; path="src\components\Branding"; type="directory"},
    @{name="Logo"; path="src\components\Logo"; type="directory"},
    @{name="TopBanner"; path="src\components\TopBanner"; type="directory"},
    @{name="HolographicCard"; path="src\components\HolographicCard"; type="directory"},
    @{name="DanceInfo"; path="src\components\DanceInfo"; type="directory"},
    @{name="RomanceCarousel"; path="src\components\RomanceCarousel"; type="directory"},
    @{name="VRMModelCard"; path="src\components\VRMModelCard"; type="directory"},
    @{name="ModelIcon"; path="src\components\ModelIcon"; type="directory"},
    @{name="ModelSelect"; path="src\components\ModelSelect"; type="directory"},
    @{name="NProgress"; path="src\components\NProgress"; type="directory"},
    @{name="VoiceSelector"; path="src\components\VoiceSelector.tsx"; type="file"},
    @{name="Application"; path="src\components\Application"; type="directory"}
)

$backupDir = "backup_components_manual"
$deletedCount = 0

Write-Host "🚀 开始删除高优先级组件..." -ForegroundColor Green

foreach ($component in $componentsToDelete) {
    $componentPath = $component.path
    $componentName = $component.name
    $componentType = $component.type
    
    if (Test-Path $componentPath) {
        Write-Host "🔄 处理组件: $componentName" -ForegroundColor Yellow
        
        try {
            # 创建备份
            $backupPath = Join-Path $backupDir $componentName
            
            if ($componentType -eq "directory") {
                if (Test-Path $backupPath) {
                    Remove-Item $backupPath -Recurse -Force
                }
                Copy-Item $componentPath $backupPath -Recurse -Force
                Remove-Item $componentPath -Recurse -Force
            } else {
                Copy-Item $componentPath $backupPath -Force
                Remove-Item $componentPath -Force
            }
            
            Write-Host "✅ 已删除: $componentPath" -ForegroundColor Green
            $deletedCount++
        }
        catch {
            Write-Host "❌ 删除失败: $componentPath - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️ 组件不存在: $componentPath" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "📊 删除完成，共删除 $deletedCount 个组件" -ForegroundColor Green
Write-Host "💾 备份目录: $backupDir" -ForegroundColor Cyan
