import { useCallback, useRef, useEffect } from 'react';
import { handleSpeakAi } from '../services/chat';
import { useInteractionManager } from './useInteractionManager';

interface Character {
  id: string;
  name: string;
  greeting?: string;
  vrm_model_config?: {
    greeting?: string;
  };
}

interface UseGreetingOptions {
  onEmotionChange?: (emotion: string) => void;
  viewer?: any;
  vrmModelLoaded?: boolean | null;
}

interface UseGreetingReturn {
  playGreetingAudio: (greeting: string, characterName: string) => Promise<void>;
  hasPlayedGreeting: boolean;
  resetGreeting: () => void;
}

/**
 * 打招呼功能Hook
 * 管理角色的欢迎语音播放，包括防重复播放、VRM动作协调等
 */
export const useGreeting = (options: UseGreetingOptions = {}): UseGreetingReturn => {
  const { onEmotionChange, viewer, vrmModelLoaded } = options;
  const hasPlayedGreeting = useRef(false);
  const interactionManager = useInteractionManager();

  /**
   * 播放欢迎语音
   * @param greeting 欢迎语内容
   * @param characterName 角色名称
   */
  const playGreetingAudio = useCallback(async (greeting: string, characterName: string) => {
    if (hasPlayedGreeting.current) {
      console.log('欢迎语音已播放过，跳过');
      return;
    }

    hasPlayedGreeting.current = true;

    try {
      console.log(`🎵 播放${characterName}欢迎语音:`, greeting);
      onEmotionChange?.('happy');
      interactionManager.setAISpeaking(true);

      // 如果VRM模型加载成功，播放greeting动作
      if (vrmModelLoaded === true && viewer?.model?.emoteController) {
        viewer.model.emoteController.playMotion('greeting', false);
      }

      await handleSpeakAi(greeting, {
        onComplete: () => {
          console.log(`✅ ${characterName}欢迎语音播放完成`);
          onEmotionChange?.('neutral');
          interactionManager.setAISpeaking(false);
          // 如果VRM模型加载成功，回到idle动作
          if (vrmModelLoaded === true && viewer?.model?.emoteController) {
            viewer.model.emoteController.playMotion('idle', true);
          }
        },
        onError: (error) => {
          console.error(`❌ ${characterName}欢迎语音播放失败:`, error);
          onEmotionChange?.('neutral');
          interactionManager.setAISpeaking(false);
          // 如果VRM模型加载成功，回到idle动作
          if (vrmModelLoaded === true && viewer?.model?.emoteController) {
            viewer.model.emoteController.playMotion('idle', true);
          }
        }
      });
    } catch (error) {
      console.error(`❌ 播放${characterName}欢迎语音失败:`, error);
      interactionManager.setAISpeaking(false);
    }
  }, [vrmModelLoaded, interactionManager, viewer, onEmotionChange]);

  /**
   * 重置打招呼状态，允许再次播放
   */
  const resetGreeting = useCallback(() => {
    hasPlayedGreeting.current = false;
    console.log('🔄 打招呼状态已重置');
  }, []);

  return {
    playGreetingAudio,
    hasPlayedGreeting: hasPlayedGreeting.current,
    resetGreeting,
  };
};

/**
 * 获取角色的欢迎语
 * @param character 角色数据
 * @returns 欢迎语文本
 */
export const getCharacterGreeting = (character: Character): string => {
  return character.vrm_model_config?.greeting ||
         character.greeting ||
         `你好，我是${character.name}，很高兴见到你！`;
};

/**
 * 自动播放欢迎语音的Hook
 * 在角色和VRM模型加载完成后自动播放欢迎语音
 */
export const useAutoGreeting = (
  character: Character | null,
  vrmModelLoaded: boolean | null,
  loading: boolean,
  greetingOptions: UseGreetingOptions = {}
) => {
  const { playGreetingAudio } = useGreeting(greetingOptions);
  const greetingTriggeredRef = useRef<string | null>(null);

  // 自动触发欢迎语音，不需要外部调用
  useEffect(() => {
    if (character && vrmModelLoaded !== null && !loading &&
        greetingTriggeredRef.current !== character.id) {

      greetingTriggeredRef.current = character.id;
      const greeting = getCharacterGreeting(character);

      console.log('🎵 准备播放欢迎语音:', character.name);

      // 延迟播放，确保页面完全加载
      const timeoutId = setTimeout(() => {
        playGreetingAudio(greeting, character.name);
      }, 2000);

      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [character?.id, vrmModelLoaded, loading, playGreetingAudio]);

  // 重置函数，用于手动重置欢迎状态
  const resetGreeting = useCallback(() => {
    greetingTriggeredRef.current = null;
  }, []);

  return { resetGreeting };
};
