@echo off
echo 🧹 开始清理未使用的组件...
echo.

:: 设置项目根目录
set PROJECT_ROOT=%~dp0virtual-character-platform-frontend
cd /d "%PROJECT_ROOT%"

echo 📁 当前工作目录: %CD%
echo.

:: 创建备份目录
set BACKUP_DIR=backup_unused_components_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_DIR=%BACKUP_DIR: =0%
echo 💾 创建备份目录: %BACKUP_DIR%
mkdir "%BACKUP_DIR%" 2>nul

:: 高优先级删除 - 品牌和营销组件
echo.
echo 🔥 第一阶段: 删除高优先级组件 (品牌和营销相关)
echo.

if exist "src\components\Analytics" (
    echo 📦 备份并删除 Analytics 组件...
    xcopy "src\components\Analytics" "%BACKUP_DIR%\Analytics\" /E /I /Q
    rmdir /S /Q "src\components\Analytics"
    echo ✅ Analytics 组件已删除
) else (
    echo ⚠️ Analytics 组件不存在
)

if exist "src\components\BrandWatermark" (
    echo 📦 备份并删除 BrandWatermark 组件...
    xcopy "src\components\BrandWatermark" "%BACKUP_DIR%\BrandWatermark\" /E /I /Q
    rmdir /S /Q "src\components\BrandWatermark"
    echo ✅ BrandWatermark 组件已删除
) else (
    echo ⚠️ BrandWatermark 组件不存在
)

if exist "src\components\Branding" (
    echo 📦 备份并删除 Branding 组件...
    xcopy "src\components\Branding" "%BACKUP_DIR%\Branding\" /E /I /Q
    rmdir /S /Q "src\components\Branding"
    echo ✅ Branding 组件已删除
) else (
    echo ⚠️ Branding 组件不存在
)

if exist "src\components\Logo" (
    echo 📦 备份并删除 Logo 组件...
    xcopy "src\components\Logo" "%BACKUP_DIR%\Logo\" /E /I /Q
    rmdir /S /Q "src\components\Logo"
    echo ✅ Logo 组件已删除
) else (
    echo ⚠️ Logo 组件不存在
)

if exist "src\components\TopBanner" (
    echo 📦 备份并删除 TopBanner 组件...
    xcopy "src\components\TopBanner" "%BACKUP_DIR%\TopBanner\" /E /I /Q
    rmdir /S /Q "src\components\TopBanner"
    echo ✅ TopBanner 组件已删除
) else (
    echo ⚠️ TopBanner 组件不存在
)

:: 高优先级删除 - 特效组件
echo.
echo 🌟 删除特效组件...
echo.

if exist "src\components\HolographicCard" (
    echo 📦 备份并删除 HolographicCard 组件...
    xcopy "src\components\HolographicCard" "%BACKUP_DIR%\HolographicCard\" /E /I /Q
    rmdir /S /Q "src\components\HolographicCard"
    echo ✅ HolographicCard 组件已删除
) else (
    echo ⚠️ HolographicCard 组件不存在
)

:: 高优先级删除 - 业务特定组件
echo.
echo 💼 删除业务特定组件...
echo.

if exist "src\components\DanceInfo" (
    echo 📦 备份并删除 DanceInfo 组件...
    xcopy "src\components\DanceInfo" "%BACKUP_DIR%\DanceInfo\" /E /I /Q
    rmdir /S /Q "src\components\DanceInfo"
    echo ✅ DanceInfo 组件已删除
) else (
    echo ⚠️ DanceInfo 组件不存在
)

if exist "src\components\RomanceCarousel" (
    echo 📦 备份并删除 RomanceCarousel 组件...
    xcopy "src\components\RomanceCarousel" "%BACKUP_DIR%\RomanceCarousel\" /E /I /Q
    rmdir /S /Q "src\components\RomanceCarousel"
    echo ✅ RomanceCarousel 组件已删除
) else (
    echo ⚠️ RomanceCarousel 组件不存在
)

if exist "src\components\VRMModelCard" (
    echo 📦 备份并删除 VRMModelCard 组件...
    xcopy "src\components\VRMModelCard" "%BACKUP_DIR%\VRMModelCard\" /E /I /Q
    rmdir /S /Q "src\components\VRMModelCard"
    echo ✅ VRMModelCard 组件已删除
) else (
    echo ⚠️ VRMModelCard 组件不存在
)

:: 高优先级删除 - 工具组件
echo.
echo 🔧 删除未使用的工具组件...
echo.

if exist "src\components\ModelIcon" (
    echo 📦 备份并删除 ModelIcon 组件...
    xcopy "src\components\ModelIcon" "%BACKUP_DIR%\ModelIcon\" /E /I /Q
    rmdir /S /Q "src\components\ModelIcon"
    echo ✅ ModelIcon 组件已删除
) else (
    echo ⚠️ ModelIcon 组件不存在
)

if exist "src\components\ModelSelect" (
    echo 📦 备份并删除 ModelSelect 组件...
    xcopy "src\components\ModelSelect" "%BACKUP_DIR%\ModelSelect\" /E /I /Q
    rmdir /S /Q "src\components\ModelSelect"
    echo ✅ ModelSelect 组件已删除
) else (
    echo ⚠️ ModelSelect 组件不存在
)

if exist "src\components\NProgress" (
    echo 📦 备份并删除 NProgress 组件...
    xcopy "src\components\NProgress" "%BACKUP_DIR%\NProgress\" /E /I /Q
    rmdir /S /Q "src\components\NProgress"
    echo ✅ NProgress 组件已删除
) else (
    echo ⚠️ NProgress 组件不存在
)

if exist "src\components\VoiceSelector.tsx" (
    echo 📦 备份并删除 VoiceSelector 组件...
    copy "src\components\VoiceSelector.tsx" "%BACKUP_DIR%\" >nul
    del "src\components\VoiceSelector.tsx"
    echo ✅ VoiceSelector 组件已删除
) else (
    echo ⚠️ VoiceSelector 组件不存在
)

if exist "src\components\Application" (
    echo 📦 备份并删除 Application 组件...
    xcopy "src\components\Application" "%BACKUP_DIR%\Application\" /E /I /Q
    rmdir /S /Q "src\components\Application"
    echo ✅ Application 组件已删除
) else (
    echo ⚠️ Application 组件不存在
)

:: 第一阶段完成
echo.
echo ✅ 第一阶段清理完成！
echo.
echo 🧪 正在运行构建测试...
call npm run build
if %ERRORLEVEL% neq 0 (
    echo ❌ 构建失败！请检查删除的组件是否被使用。
    echo 💾 备份文件位于: %BACKUP_DIR%
    pause
    exit /b 1
)
echo ✅ 构建测试通过！

echo.
echo 🤔 是否继续第二阶段清理 (中优先级组件)? 
echo 警告: 这些组件可能被使用，删除前请确认！
echo.
set /p CONTINUE="输入 Y 继续，任意键跳过: "
if /i "%CONTINUE%" neq "Y" (
    echo 🛑 跳过第二阶段清理
    goto :summary
)

:: 第二阶段 - 中优先级删除
echo.
echo 🟡 第二阶段: 删除中优先级组件 (需要确认)
echo.

if exist "src\components\ChatItem_Legacy" (
    echo 📦 备份并删除 ChatItem_Legacy 组件...
    xcopy "src\components\ChatItem_Legacy" "%BACKUP_DIR%\ChatItem_Legacy\" /E /I /Q
    rmdir /S /Q "src\components\ChatItem_Legacy"
    echo ✅ ChatItem_Legacy 组件已删除
) else (
    echo ⚠️ ChatItem_Legacy 组件不存在
)

if exist "src\components\Error" (
    echo 📦 备份并删除 Error 组件...
    xcopy "src\components\Error" "%BACKUP_DIR%\Error\" /E /I /Q
    rmdir /S /Q "src\components\Error"
    echo ✅ Error 组件已删除
) else (
    echo ⚠️ Error 组件不存在
)

if exist "src\components\Menu" (
    echo 📦 备份并删除 Menu 组件...
    xcopy "src\components\Menu" "%BACKUP_DIR%\Menu\" /E /I /Q
    rmdir /S /Q "src\components\Menu"
    echo ✅ Menu 组件已删除
) else (
    echo ⚠️ Menu 组件不存在
)

if exist "src\components\PanelTitle" (
    echo 📦 备份并删除 PanelTitle 组件...
    xcopy "src\components\PanelTitle" "%BACKUP_DIR%\PanelTitle\" /E /I /Q
    rmdir /S /Q "src\components\PanelTitle"
    echo ✅ PanelTitle 组件已删除
) else (
    echo ⚠️ PanelTitle 组件不存在
)

if exist "src\components\RoleCard" (
    echo 📦 备份并删除 RoleCard 组件...
    xcopy "src\components\RoleCard" "%BACKUP_DIR%\RoleCard\" /E /I /Q
    rmdir /S /Q "src\components\RoleCard"
    echo ✅ RoleCard 组件已删除
) else (
    echo ⚠️ RoleCard 组件不存在
)

if exist "src\components\TextArea" (
    echo 📦 备份并删除 TextArea 组件...
    xcopy "src\components\TextArea" "%BACKUP_DIR%\TextArea\" /E /I /Q
    rmdir /S /Q "src\components\TextArea"
    echo ✅ TextArea 组件已删除
) else (
    echo ⚠️ TextArea 组件不存在
)

if exist "src\components\server" (
    echo 📦 备份并删除 server 组件目录...
    xcopy "src\components\server" "%BACKUP_DIR%\server\" /E /I /Q
    rmdir /S /Q "src\components\server"
    echo ✅ server 组件目录已删除
) else (
    echo ⚠️ server 组件目录不存在
)

echo.
echo 🧪 正在运行第二阶段构建测试...
call npm run build
if %ERRORLEVEL% neq 0 (
    echo ❌ 构建失败！请检查删除的组件是否被使用。
    echo 💾 备份文件位于: %BACKUP_DIR%
    pause
    exit /b 1
)
echo ✅ 第二阶段构建测试通过！

:summary
echo.
echo 🎉 组件清理完成！
echo.
echo 📊 清理总结:
echo ================
echo 💾 备份目录: %BACKUP_DIR%
echo 🗑️ 已删除的高优先级组件:
echo   - Analytics (分析组件)
echo   - BrandWatermark (品牌水印)
echo   - Branding (品牌组件)
echo   - Logo (Logo组件)
echo   - TopBanner (顶部横幅)
echo   - HolographicCard (全息卡片)
echo   - DanceInfo (舞蹈信息)
echo   - RomanceCarousel (浪漫轮播)
echo   - VRMModelCard (VRM模型卡片)
echo   - ModelIcon (模型图标)
echo   - ModelSelect (模型选择)
echo   - NProgress (进度条)
echo   - VoiceSelector (语音选择器)
echo   - Application (应用程序组件)
echo.
if /i "%CONTINUE%" equ "Y" (
    echo 🗑️ 已删除的中优先级组件:
    echo   - ChatItem_Legacy (遗留聊天项)
    echo   - Error (错误组件)
    echo   - Menu (菜单组件)
    echo   - PanelTitle (面板标题)
    echo   - RoleCard (角色卡片)
    echo   - TextArea (文本区域)
    echo   - server (服务器组件目录)
    echo.
)
echo 💡 建议:
echo   1. 运行完整的功能测试
echo   2. 检查所有页面是否正常工作
echo   3. 如有问题，可从备份目录恢复
echo   4. 确认无问题后可删除备份目录
echo.
echo ✅ 清理完成！项目现在更加简洁了。

echo.
echo 🔍 运行类型检查...
call npm run type-check
if %ERRORLEVEL% neq 0 (
    echo ⚠️ 类型检查发现问题，请检查代码
) else (
    echo ✅ 类型检查通过
)

echo.
echo 📝 生成清理报告...
echo 组件清理报告 - %date% %time% > "%BACKUP_DIR%\cleanup_report.txt"
echo ================================== >> "%BACKUP_DIR%\cleanup_report.txt"
echo 备份目录: %BACKUP_DIR% >> "%BACKUP_DIR%\cleanup_report.txt"
echo 清理时间: %date% %time% >> "%BACKUP_DIR%\cleanup_report.txt"
echo 清理的组件数量: 14+ >> "%BACKUP_DIR%\cleanup_report.txt"
echo 预估减少代码行数: 3000-5000行 >> "%BACKUP_DIR%\cleanup_report.txt"
echo 预估减少文件数: 60-80个 >> "%BACKUP_DIR%\cleanup_report.txt"

echo ✅ 清理报告已生成: %BACKUP_DIR%\cleanup_report.txt
pause
