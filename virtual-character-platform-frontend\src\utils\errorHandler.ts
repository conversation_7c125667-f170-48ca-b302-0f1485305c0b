/**
 * 统一错误处理机制
 * 提供用户友好的错误提示和处理
 */

import { message } from 'antd';

export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  PERMISSION = 'PERMISSION',
  VALIDATION = 'VALIDATION',
  TTS = 'TTS',
  VRM = 'VRM',
  AUDIO = 'AUDIO',
  UNKNOWN = 'UNKNOWN'
}

export interface ErrorInfo {
  type: ErrorType;
  code?: string;
  message: string;
  details?: any;
  userMessage?: string;
  suggestions?: string[];
}

/**
 * 错误分类器 - 根据错误内容自动分类
 */
export const classifyError = (error: any): ErrorInfo => {
  const errorMessage = error?.message || error?.toString() || '未知错误';
  const errorCode = error?.code || error?.status;

  // 网络错误
  if (errorMessage.includes('fetch') || 
      errorMessage.includes('network') || 
      errorMessage.includes('连接') ||
      errorCode === 'NETWORK_ERROR') {
    return {
      type: ErrorType.NETWORK,
      code: errorCode,
      message: errorMessage,
      userMessage: '网络连接失败，请检查网络设置',
      suggestions: [
        '检查网络连接是否正常',
        '尝试刷新页面',
        '联系技术支持'
      ]
    };
  }

  // 认证错误
  if (errorCode === 401 || 
      errorMessage.includes('unauthorized') || 
      errorMessage.includes('认证') ||
      errorMessage.includes('登录')) {
    return {
      type: ErrorType.AUTHENTICATION,
      code: errorCode,
      message: errorMessage,
      userMessage: '身份验证失败，请重新登录',
      suggestions: [
        '检查登录状态',
        '重新登录账户',
        '清除浏览器缓存'
      ]
    };
  }

  // 权限错误
  if (errorMessage.includes('permission') || 
      errorMessage.includes('权限') ||
      errorMessage.includes('拒绝')) {
    return {
      type: ErrorType.PERMISSION,
      code: errorCode,
      message: errorMessage,
      userMessage: '权限不足或被拒绝',
      suggestions: [
        '检查浏览器权限设置',
        '允许网站访问所需功能',
        '尝试重新授权'
      ]
    };
  }

  // TTS错误
  if (errorMessage.includes('TTS') || 
      errorMessage.includes('语音合成') ||
      errorMessage.includes('speech')) {
    return {
      type: ErrorType.TTS,
      code: errorCode,
      message: errorMessage,
      userMessage: '语音合成失败',
      suggestions: [
        '检查网络连接',
        '尝试切换语音引擎',
        '稍后重试'
      ]
    };
  }

  // 音频错误
  if (errorMessage.includes('audio') || 
      errorMessage.includes('音频') ||
      errorMessage.includes('AudioContext')) {
    return {
      type: ErrorType.AUDIO,
      code: errorCode,
      message: errorMessage,
      userMessage: '音频播放失败',
      suggestions: [
        '检查浏览器音频权限',
        '确保设备音量已开启',
        '尝试点击页面激活音频'
      ]
    };
  }

  // VRM错误
  if (errorMessage.includes('VRM') || 
      errorMessage.includes('模型') ||
      errorMessage.includes('3D')) {
    return {
      type: ErrorType.VRM,
      code: errorCode,
      message: errorMessage,
      userMessage: '3D模型加载失败',
      suggestions: [
        '检查网络连接',
        '确保浏览器支持WebGL',
        '尝试刷新页面'
      ]
    };
  }

  // 验证错误
  if (errorMessage.includes('validation') || 
      errorMessage.includes('验证') ||
      errorMessage.includes('格式')) {
    return {
      type: ErrorType.VALIDATION,
      code: errorCode,
      message: errorMessage,
      userMessage: '输入数据格式错误',
      suggestions: [
        '检查输入内容格式',
        '确保必填字段已填写',
        '参考输入示例'
      ]
    };
  }

  // 默认未知错误
  return {
    type: ErrorType.UNKNOWN,
    code: errorCode,
    message: errorMessage,
    userMessage: '操作失败，请稍后重试',
    suggestions: [
      '刷新页面重试',
      '检查网络连接',
      '联系技术支持'
    ]
  };
};

/**
 * 显示错误提示
 */
export const showError = (error: any, options?: {
  showDetails?: boolean;
  duration?: number;
  showSuggestions?: boolean;
}) => {
  const errorInfo = classifyError(error);
  const { showDetails = false, duration = 4, showSuggestions = false } = options || {};

  let content = errorInfo.userMessage || errorInfo.message;
  
  if (showDetails && errorInfo.details) {
    content += `\n详细信息: ${JSON.stringify(errorInfo.details)}`;
  }
  
  if (showSuggestions && errorInfo.suggestions) {
    content += `\n建议: ${errorInfo.suggestions.join(', ')}`;
  }

  // 根据错误类型选择不同的提示方式
  switch (errorInfo.type) {
    case ErrorType.NETWORK:
    case ErrorType.AUTHENTICATION:
      message.error(content, duration);
      break;
    case ErrorType.PERMISSION:
      message.warning(content, duration);
      break;
    case ErrorType.VALIDATION:
      message.warning(content, duration);
      break;
    case ErrorType.TTS:
    case ErrorType.AUDIO:
    case ErrorType.VRM:
      message.error(content, duration);
      break;
    default:
      message.error(content, duration);
  }

  // 控制台输出详细错误信息
  console.error(`[${errorInfo.type}] ${errorInfo.message}`, {
    code: errorInfo.code,
    details: errorInfo.details,
    suggestions: errorInfo.suggestions,
    originalError: error
  });

  return errorInfo;
};

/**
 * 错误边界处理器
 */
export const handleError = (error: any, context?: string) => {
  const errorInfo = classifyError(error);
  
  // 添加上下文信息
  if (context) {
    errorInfo.details = {
      ...errorInfo.details,
      context
    };
  }

  showError(error, { showSuggestions: true });
  
  return errorInfo;
};

/**
 * 异步操作错误处理装饰器
 */
export const withErrorHandler = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  context?: string
): T => {
  return (async (...args: any[]) => {
    try {
      return await fn(...args);
    } catch (error) {
      handleError(error, context);
      throw error;
    }
  }) as T;
};

/**
 * 网络请求错误处理
 */
export const handleNetworkError = (error: any, url?: string) => {
  const errorInfo = classifyError(error);
  
  if (url) {
    errorInfo.details = {
      ...errorInfo.details,
      url
    };
  }

  // 特殊处理常见HTTP状态码
  if (errorInfo.code === 404) {
    errorInfo.userMessage = '请求的资源不存在';
  } else if (errorInfo.code === 500) {
    errorInfo.userMessage = '服务器内部错误，请稍后重试';
  } else if (errorInfo.code === 403) {
    errorInfo.userMessage = '访问被拒绝，权限不足';
  }

  showError(errorInfo);
  return errorInfo;
};
