#!/usr/bin/env python3
"""
测试Edge TTS安装和功能
"""

import os
import sys
import asyncio
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'virtual_character_platform.settings')
django.setup()

def test_edge_tts_import():
    """测试Edge TTS库导入"""
    print("🧪 测试Edge TTS库导入...")
    
    try:
        import edge_tts
        print("✅ Edge TTS库导入成功")
        
        # 检查版本
        if hasattr(edge_tts, '__version__'):
            print(f"📦 版本: {edge_tts.__version__}")
        else:
            print("📦 版本: 未知")
        
        return True
    except ImportError as e:
        print(f"❌ Edge TTS库导入失败: {e}")
        return False

async def test_edge_tts_voices():
    """测试获取语音列表"""
    print("\n🧪 测试获取语音列表...")
    
    try:
        import edge_tts
        voices = await edge_tts.list_voices()
        
        if voices:
            print(f"✅ 成功获取 {len(voices)} 个语音")
            
            # 显示中文语音
            chinese_voices = [v for v in voices if 'zh-CN' in v['Locale']]
            print(f"🇨🇳 中文语音数量: {len(chinese_voices)}")
            
            # 显示前5个中文语音
            for i, voice in enumerate(chinese_voices[:5]):
                print(f"  {i+1}. {voice['ShortName']} - {voice['FriendlyName']}")
            
            return True
        else:
            print("❌ 未获取到语音列表")
            return False
            
    except Exception as e:
        print(f"❌ 获取语音列表失败: {e}")
        return False

async def test_edge_tts_synthesis():
    """测试语音合成"""
    print("\n🧪 测试语音合成...")
    
    try:
        import edge_tts
        
        # 测试文本
        text = "你好，这是Edge TTS语音合成测试。"
        voice = "zh-CN-XiaoxiaoNeural"
        
        print(f"📝 测试文本: {text}")
        print(f"🎤 使用语音: {voice}")
        
        # 创建通信对象
        communicate = edge_tts.Communicate(text, voice)
        
        # 生成音频
        audio_data = b""
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_data += chunk["data"]
        
        if audio_data:
            print(f"✅ 语音合成成功，音频大小: {len(audio_data)} bytes")
            
            # 保存测试音频
            output_file = "test_edge_tts_output.mp3"
            with open(output_file, "wb") as f:
                f.write(audio_data)
            print(f"💾 测试音频已保存: {output_file}")
            
            return True
        else:
            print("❌ 语音合成失败，未生成音频数据")
            return False
            
    except Exception as e:
        print(f"❌ 语音合成测试失败: {e}")
        return False

def test_django_edge_tts_view():
    """测试Django Edge TTS视图"""
    print("\n🧪 测试Django Edge TTS视图...")
    
    try:
        from core.voice_views import EdgeTTSView
        from django.test import RequestFactory
        from django.http import JsonResponse
        import json
        
        # 创建测试请求
        factory = RequestFactory()
        test_data = {
            'input': '你好，这是Django Edge TTS测试。',
            'options': {
                'voice': 'zh-CN-XiaoxiaoNeural',
                'rate': 0,
                'pitch': 0
            }
        }
        
        request = factory.post(
            '/api/voice/edge/',
            data=json.dumps(test_data),
            content_type='application/json'
        )
        
        # 创建视图实例并处理请求
        view = EdgeTTSView()
        response = view.post(request)
        
        if response.status_code == 200:
            print("✅ Django Edge TTS视图测试成功")
            print(f"📊 响应状态码: {response.status_code}")
            
            # 检查响应内容
            if hasattr(response, 'content') and response.content:
                print(f"🎵 音频数据大小: {len(response.content)} bytes")
            
            return True
        else:
            print(f"❌ Django Edge TTS视图测试失败，状态码: {response.status_code}")
            if hasattr(response, 'content'):
                print(f"错误信息: {response.content.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Django Edge TTS视图测试异常: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 Edge TTS安装和功能测试开始\n")
    
    results = []
    
    # 测试1: 库导入
    results.append(test_edge_tts_import())
    
    # 测试2: 语音列表
    if results[-1]:
        results.append(await test_edge_tts_voices())
    else:
        results.append(False)
    
    # 测试3: 语音合成
    if results[-1]:
        results.append(await test_edge_tts_synthesis())
    else:
        results.append(False)
    
    # 测试4: Django视图
    results.append(test_django_edge_tts_view())
    
    # 总结
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print("="*50)
    
    test_names = [
        "Edge TTS库导入",
        "获取语音列表", 
        "语音合成功能",
        "Django视图集成"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！Edge TTS已成功安装并可以正常使用。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关配置。")
        return False

if __name__ == "__main__":
    try:
        # 运行异步测试
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        sys.exit(1)
