const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 已删除的组件列表
const DELETED_COMPONENTS = [
  'Analytics', 'BrandWatermark', 'Branding', 'Logo', 'TopBanner',
  'HolographicCard', 'DanceInfo', 'RomanceCarousel', 'VRMModelCard',
  'ModelIcon', 'ModelSelect', 'NProgress', 'VoiceSelector', 'Application',
  'ChatItem_Legacy', 'PanelTitle', 'ServerLayout', 'MobileNavLayout'
];

// 查找包含已删除组件导入的文件
function findFilesWithDeletedImports() {
  console.log('🔍 查找包含已删除组件导入的文件...\n');
  
  const results = [];
  
  for (const component of DELETED_COMPONENTS) {
    try {
      // 查找import语句
      const importPattern = `import.*${component}`;
      const fromPattern = `from.*${component}`;
      
      const patterns = [importPattern, fromPattern];
      
      for (const pattern of patterns) {
        try {
          const result = execSync(
            `powershell -Command "Get-ChildItem -Path src\\ -Recurse -Include *.tsx,*.ts,*.js,*.jsx | Select-String -Pattern '${pattern}' | ForEach-Object { $_.Filename + ' -> ' + $_.RelativePath }"`,
            { encoding: 'utf8' }
          );
          
          if (result.trim()) {
            const files = result.trim().split('\n').filter(f => f.trim());
            files.forEach(file => {
              if (!results.find(r => r.file === file && r.component === component)) {
                results.push({ file: file.trim(), component, pattern });
              }
            });
          }
        } catch (error) {
          // 忽略错误，继续下一个模式
        }
      }
    } catch (error) {
      console.log(`⚠️ 检查组件 ${component} 时出错: ${error.message}`);
    }
  }
  
  return results;
}

// 清理文件中的无用导入
function cleanupImportsInFile(filePath, component) {
  try {
    const fullPath = path.join('src', filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️ 文件不存在: ${fullPath}`);
      return false;
    }
    
    const content = fs.readFileSync(fullPath, 'utf8');
    const lines = content.split('\n');
    const cleanedLines = [];
    let hasChanges = false;
    
    for (const line of lines) {
      // 检查是否是包含已删除组件的导入行
      if (line.includes('import') && line.includes(component)) {
        console.log(`  🗑️ 删除导入行: ${line.trim()}`);
        hasChanges = true;
        // 添加注释说明
        cleanedLines.push(`// ${line.trim()} // 组件已删除`);
      } else {
        cleanedLines.push(line);
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(fullPath, cleanedLines.join('\n'), 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.log(`❌ 清理文件 ${filePath} 时出错: ${error.message}`);
    return false;
  }
}

// 主函数
async function main() {
  console.log('🧹 开始清理无用的导入语句...\n');
  
  const filesWithImports = findFilesWithDeletedImports();
  
  if (filesWithImports.length === 0) {
    console.log('✅ 没有找到需要清理的导入语句');
    return;
  }
  
  console.log(`📋 找到 ${filesWithImports.length} 个需要清理的导入引用:\n`);
  
  // 按文件分组
  const fileGroups = {};
  filesWithImports.forEach(item => {
    if (!fileGroups[item.file]) {
      fileGroups[item.file] = [];
    }
    fileGroups[item.file].push(item.component);
  });
  
  let cleanedFiles = 0;
  
  for (const [file, components] of Object.entries(fileGroups)) {
    console.log(`🔧 清理文件: ${file}`);
    console.log(`   组件: ${components.join(', ')}`);
    
    let fileChanged = false;
    for (const component of components) {
      if (cleanupImportsInFile(file, component)) {
        fileChanged = true;
      }
    }
    
    if (fileChanged) {
      cleanedFiles++;
      console.log(`   ✅ 已清理`);
    } else {
      console.log(`   ⚠️ 无需更改`);
    }
    console.log('');
  }
  
  console.log(`📊 清理完成:`);
  console.log(`   - 检查的文件: ${Object.keys(fileGroups).length} 个`);
  console.log(`   - 清理的文件: ${cleanedFiles} 个`);
  console.log(`   - 涉及的组件: ${DELETED_COMPONENTS.length} 个`);
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  findFilesWithDeletedImports,
  cleanupImportsInFile,
  DELETED_COMPONENTS
};
