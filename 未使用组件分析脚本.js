// 未使用组件分析脚本
// 在浏览器控制台中运行此脚本来分析项目中未被使用的组件

(function() {
    console.log('🔍 开始分析未使用的组件...');
    
    // 定义所有组件文件列表（基于目录结构）
    const allComponents = [
        // 根目录组件
        'Avatar.tsx',
        'CharacterVoicePlayer.tsx',
        'ErrorBoundary.tsx',
        'ErrorRecovery.tsx',
        'Footer.tsx',
        'GlobalErrorHandler.tsx',
        'Header.tsx',
        'LazyImage.tsx',
        'MainLayout.tsx',
        'NetworkStatusMonitor.tsx',
        'OptimizedImage.tsx',
        'PerformanceMonitor.tsx',
        'SafeContent.tsx',
        'Sidebar.tsx',
        'SkeletonList.tsx',
        'SpeechRecognitionErrorBoundary.tsx',
        'StopLoading.tsx',
        'TouchInteractionWrapper.tsx',
        'VoiceControls.tsx',
        'VoiceSelector.tsx',
        
        // Analytics目录
        'Analytics/index.tsx',
        
        // Application目录
        'Application/index.tsx',
        
        // Author目录
        'Author/index.tsx',
        
        // BrandWatermark目录
        'BrandWatermark/index.tsx',
        
        // Branding目录
        'Branding/index.tsx',
        
        // ChatItem目录
        'ChatItem/index.tsx',
        'ChatItem/Actions.tsx',
        'ChatItem/Avatar.tsx',
        'ChatItem/ErrorContent.tsx',
        'ChatItem/MessageContent.tsx',
        'ChatItem/Title.tsx',
        
        // ChatItem_Legacy目录
        'ChatItem_Legacy/index.tsx',
        
        // CircleLoading目录
        'CircleLoading/index.tsx',
        
        // DanceInfo目录
        'DanceInfo/index.tsx',
        
        // Error目录
        'Error/index.tsx',
        
        // GridList目录
        'GridList/index.tsx',
        
        // HolographicCard目录
        'HolographicCard/index.tsx',
        'HolographicCard/Container.tsx',
        'HolographicCard/LaserShine.tsx',
        'HolographicCard/Orbit.tsx',
        
        // ListItem目录
        'ListItem/index.tsx',
        
        // Logo目录
        'Logo/index.tsx',
        
        // Menu目录
        'Menu/index.tsx',
        
        // ModelIcon目录
        'ModelIcon/index.tsx',
        
        // ModelSelect目录
        'ModelSelect/index.tsx',
        
        // NProgress目录
        'NProgress/index.tsx',
        
        // PageLoading目录
        'PageLoading/index.tsx',
        
        // PanelTitle目录
        'PanelTitle/index.tsx',
        
        // RoleCard目录
        'RoleCard/index.tsx',
        
        // RomanceCarousel目录
        'RomanceCarousel/index.tsx',
        
        // ScreenLoading目录
        'ScreenLoading/index.tsx',
        
        // TextArea目录
        'TextArea/index.tsx',
        
        // TopBanner目录
        'TopBanner/index.tsx',
        
        // VRMModelCard目录
        'VRMModelCard/index.tsx',
        
        // admin目录
        'admin/AdminLayout.tsx',
        'admin/AdminProtectedRoute.tsx',
        
        // agent目录
        'agent/AgentCard/index.tsx',
        'agent/SystemRole/index.tsx',
        
        // character目录
        'character/BackgroundGenerationStatus.tsx',
        'character/IdentitySelector.tsx',
        'character/PersonalitySelector.tsx',
        'character/PersonalityIdentitySelector.tsx',
        
        // chat目录
        'chat/BottomChatBox.tsx',
        'chat/ChatInput.tsx',
        'chat/MessageList.tsx',
        
        // role目录
        'role/RoleCreator.tsx',
        'role/RoleEditor.tsx',
        'role/RoleList.tsx',
        
        // server目录
        'server/MobileNavLayout.tsx',
        'server/ServerLayout.tsx'
    ];
    
    // 已知被使用的组件（从App.tsx和主要页面分析得出）
    const usedComponents = [
        'MainLayout.tsx',
        'Sidebar.tsx',
        'VoiceControls.tsx',
        'TouchInteractionWrapper.tsx',
        'SpeechRecognitionErrorBoundary.tsx',
        'admin/AdminLayout.tsx',
        'admin/AdminProtectedRoute.tsx',
        'chat/BottomChatBox.tsx',
        'ErrorBoundary.tsx',
        'LazyImage.tsx',
        'OptimizedImage.tsx',
        'PerformanceMonitor.tsx'
    ];
    
    // 可能被使用的组件（需要进一步检查）
    const possiblyUsedComponents = [
        'Avatar.tsx',
        'CharacterVoicePlayer.tsx',
        'Header.tsx',
        'Footer.tsx',
        'SafeContent.tsx',
        'ChatItem/index.tsx',
        'ChatItem/Actions.tsx',
        'ChatItem/Avatar.tsx',
        'ChatItem/ErrorContent.tsx',
        'ChatItem/MessageContent.tsx',
        'ChatItem/Title.tsx',
        'GridList/index.tsx',
        'ListItem/index.tsx',
        'PageLoading/index.tsx',
        'CircleLoading/index.tsx',
        'ScreenLoading/index.tsx',
        'agent/AgentCard/index.tsx',
        'character/IdentitySelector.tsx',
        'character/PersonalitySelector.tsx',
        'role/RoleCreator.tsx',
        'role/RoleEditor.tsx',
        'role/RoleList.tsx'
    ];
    
    // 分析未使用的组件
    const unusedComponents = allComponents.filter(component => 
        !usedComponents.includes(component) && 
        !possiblyUsedComponents.includes(component)
    );
    
    // 生成分析报告
    console.log('\n📊 组件使用情况分析报告');
    console.log('='.repeat(50));
    
    console.log(`\n✅ 确认被使用的组件 (${usedComponents.length}个):`);
    usedComponents.forEach(component => {
        console.log(`  ✅ ${component}`);
    });
    
    console.log(`\n❓ 可能被使用的组件 (${possiblyUsedComponents.length}个):`);
    possiblyUsedComponents.forEach(component => {
        console.log(`  ❓ ${component}`);
    });
    
    console.log(`\n❌ 疑似未使用的组件 (${unusedComponents.length}个):`);
    unusedComponents.forEach(component => {
        console.log(`  ❌ ${component}`);
    });
    
    // 按类别分析未使用组件
    const categorizeUnusedComponents = () => {
        const categories = {
            '加载相关': [],
            '卡片相关': [],
            '导航相关': [],
            '工具组件': [],
            '特效组件': [],
            '业务组件': [],
            '其他': []
        };
        
        unusedComponents.forEach(component => {
            if (component.includes('Loading') || component.includes('Progress')) {
                categories['加载相关'].push(component);
            } else if (component.includes('Card') || component.includes('List')) {
                categories['卡片相关'].push(component);
            } else if (component.includes('Menu') || component.includes('Nav')) {
                categories['导航相关'].push(component);
            } else if (component.includes('Analytics') || component.includes('Monitor') || component.includes('Error')) {
                categories['工具组件'].push(component);
            } else if (component.includes('Holographic') || component.includes('Brand') || component.includes('Logo')) {
                categories['特效组件'].push(component);
            } else if (component.includes('Dance') || component.includes('Romance') || component.includes('VRM')) {
                categories['业务组件'].push(component);
            } else {
                categories['其他'].push(component);
            }
        });
        
        return categories;
    };
    
    const categorizedUnused = categorizeUnusedComponents();
    
    console.log('\n📋 未使用组件分类统计:');
    console.log('='.repeat(30));
    
    Object.entries(categorizedUnused).forEach(([category, components]) => {
        if (components.length > 0) {
            console.log(`\n${category} (${components.length}个):`);
            components.forEach(component => {
                console.log(`  - ${component}`);
            });
        }
    });
    
    // 生成清理建议
    console.log('\n💡 清理建议:');
    console.log('='.repeat(20));
    
    const highPriorityCleanup = [
        'Analytics/index.tsx',
        'Application/index.tsx',
        'BrandWatermark/index.tsx',
        'Branding/index.tsx',
        'DanceInfo/index.tsx',
        'HolographicCard/',
        'Logo/index.tsx',
        'ModelIcon/index.tsx',
        'ModelSelect/index.tsx',
        'NProgress/index.tsx',
        'RomanceCarousel/index.tsx',
        'TopBanner/index.tsx',
        'VRMModelCard/index.tsx'
    ];
    
    const mediumPriorityCleanup = [
        'ChatItem_Legacy/',
        'Error/index.tsx',
        'Menu/index.tsx',
        'PanelTitle/index.tsx',
        'RoleCard/index.tsx',
        'TextArea/index.tsx',
        'server/ServerLayout.tsx'
    ];
    
    const lowPriorityCleanup = [
        'Author/index.tsx',
        'StopLoading.tsx',
        'NetworkStatusMonitor.tsx',
        'VoiceSelector.tsx'
    ];
    
    console.log('\n🔥 高优先级清理 (可以安全删除):');
    highPriorityCleanup.forEach(component => {
        if (unusedComponents.some(unused => unused.includes(component.replace('/', '')))) {
            console.log(`  🗑️ ${component}`);
        }
    });
    
    console.log('\n🟡 中优先级清理 (需要确认):');
    mediumPriorityCleanup.forEach(component => {
        if (unusedComponents.some(unused => unused.includes(component.replace('/', '')))) {
            console.log(`  ⚠️ ${component}`);
        }
    });
    
    console.log('\n🟢 低优先级清理 (保留观察):');
    lowPriorityCleanup.forEach(component => {
        if (unusedComponents.some(unused => unused.includes(component.replace('/', '')))) {
            console.log(`  📋 ${component}`);
        }
    });
    
    // 统计信息
    console.log('\n📈 统计信息:');
    console.log('='.repeat(15));
    console.log(`总组件数: ${allComponents.length}`);
    console.log(`已使用: ${usedComponents.length} (${Math.round(usedComponents.length/allComponents.length*100)}%)`);
    console.log(`可能使用: ${possiblyUsedComponents.length} (${Math.round(possiblyUsedComponents.length/allComponents.length*100)}%)`);
    console.log(`疑似未使用: ${unusedComponents.length} (${Math.round(unusedComponents.length/allComponents.length*100)}%)`);
    
    // 保存结果到全局变量
    window.componentAnalysisResult = {
        total: allComponents.length,
        used: usedComponents,
        possiblyUsed: possiblyUsedComponents,
        unused: unusedComponents,
        categorized: categorizedUnused,
        cleanup: {
            high: highPriorityCleanup,
            medium: mediumPriorityCleanup,
            low: lowPriorityCleanup
        }
    };
    
    console.log('\n💾 分析结果已保存到 window.componentAnalysisResult');
    
    return window.componentAnalysisResult;
})();
