#!/usr/bin/env python3
"""
测试Edge TTS API
"""

import requests
import json
import time

def test_edge_tts_api():
    """测试Edge TTS API"""
    print("🧪 测试Edge TTS API...")
    
    # API端点
    url = "http://localhost:8000/api/voice/edge/"
    
    # 测试数据
    payload = {
        "input": "你好，这是Edge TTS API测试。",
        "options": {
            "voice": "zh-CN-XiaoxiaoNeural",
            "rate": 0,
            "pitch": 0
        }
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"📡 发送请求到: {url}")
        print(f"📝 请求数据: {json.dumps(payload, ensure_ascii=False)}")
        
        # 发送请求
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ API请求成功")
            
            # 检查响应内容
            if response.content:
                print(f"🎵 音频数据大小: {len(response.content)} bytes")
                
                # 保存音频文件
                output_file = "test_api_output.mp3"
                with open(output_file, "wb") as f:
                    f.write(response.content)
                print(f"💾 音频文件已保存: {output_file}")
                
                return True
            else:
                print("❌ 响应内容为空")
                return False
        else:
            print(f"❌ API请求失败，状态码: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
            except:
                print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保Django服务器正在运行")
        print("💡 启动命令: python manage.py runserver")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_multiple_voices():
    """测试多个语音"""
    print("\n🧪 测试多个语音...")
    
    voices = [
        "zh-CN-XiaoxiaoNeural",
        "zh-CN-YunxiNeural", 
        "zh-CN-XiaoyiNeural"
    ]
    
    results = []
    
    for voice in voices:
        print(f"\n🎤 测试语音: {voice}")
        
        payload = {
            "input": f"你好，我是{voice}语音。",
            "options": {
                "voice": voice,
                "rate": 0,
                "pitch": 0
            }
        }
        
        try:
            response = requests.post(
                "http://localhost:8000/api/voice/edge/",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200 and response.content:
                print(f"✅ {voice} 测试成功，音频大小: {len(response.content)} bytes")
                results.append(True)
                
                # 保存音频文件
                output_file = f"test_{voice.replace('-', '_')}.mp3"
                with open(output_file, "wb") as f:
                    f.write(response.content)
                print(f"💾 已保存: {output_file}")
            else:
                print(f"❌ {voice} 测试失败")
                results.append(False)
                
        except Exception as e:
            print(f"❌ {voice} 测试异常: {e}")
            results.append(False)
        
        # 避免请求过于频繁
        time.sleep(1)
    
    success_count = sum(results)
    print(f"\n📊 语音测试结果: {success_count}/{len(voices)} 成功")
    
    return success_count > 0

def main():
    """主函数"""
    print("🚀 Edge TTS API测试开始\n")
    
    # 基础API测试
    basic_test = test_edge_tts_api()
    
    if basic_test:
        # 多语音测试
        voice_test = test_multiple_voices()
    else:
        voice_test = False
    
    # 总结
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print("="*50)
    
    print(f"1. 基础API测试: {'✅ 通过' if basic_test else '❌ 失败'}")
    print(f"2. 多语音测试: {'✅ 通过' if voice_test else '❌ 失败'}")
    
    if basic_test and voice_test:
        print("\n🎉 所有测试通过！Edge TTS API已正常工作。")
        print("💡 现在可以在前端使用语音功能了。")
        return True
    elif basic_test:
        print("\n⚠️ 基础功能正常，但部分语音可能有问题。")
        return True
    else:
        print("\n❌ API测试失败，请检查服务器配置。")
        print("🔧 可能的解决方案：")
        print("   1. 确保Django服务器正在运行")
        print("   2. 检查edge-tts库是否正确安装")
        print("   3. 检查网络连接和防火墙设置")
        return False

if __name__ == "__main__":
    try:
        result = main()
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        exit(1)
