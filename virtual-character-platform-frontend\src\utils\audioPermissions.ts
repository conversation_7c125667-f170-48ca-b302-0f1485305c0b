/**
 * 音频权限检查和管理工具
 * 解决浏览器音频播放权限问题
 */

export interface AudioPermissionStatus {
  hasPermission: boolean;
  needsUserInteraction: boolean;
  error?: string;
}

/**
 * 检查音频播放权限
 */
export const checkAudioPermission = async (): Promise<AudioPermissionStatus> => {
  try {
    // 检查AudioContext是否可用
    if (!window.AudioContext && !(window as any).webkitAudioContext) {
      return {
        hasPermission: false,
        needsUserInteraction: false,
        error: '浏览器不支持Web Audio API'
      };
    }

    // 创建临时AudioContext测试
    const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
    const testContext = new AudioContextClass();
    
    if (testContext.state === 'suspended') {
      // 需要用户交互来恢复
      testContext.close();
      return {
        hasPermission: false,
        needsUserInteraction: true,
        error: '需要用户交互来启用音频播放'
      };
    }
    
    testContext.close();
    return {
      hasPermission: true,
      needsUserInteraction: false
    };
    
  } catch (error) {
    console.error('检查音频权限失败:', error);
    return {
      hasPermission: false,
      needsUserInteraction: false,
      error: `音频权限检查失败: ${error}`
    };
  }
};

/**
 * 请求音频播放权限（通过用户交互）
 */
export const requestAudioPermission = async (): Promise<boolean> => {
  try {
    const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
    const context = new AudioContextClass();
    
    if (context.state === 'suspended') {
      await context.resume();
    }
    
    // 播放一个静音的音频来激活权限
    const oscillator = context.createOscillator();
    const gainNode = context.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(context.destination);
    
    gainNode.gain.value = 0; // 静音
    oscillator.frequency.value = 440;
    oscillator.start();
    oscillator.stop(context.currentTime + 0.1);
    
    // 等待一小段时间确保播放完成
    await new Promise(resolve => setTimeout(resolve, 200));
    
    const hasPermission = context.state === 'running';
    context.close();
    
    return hasPermission;
    
  } catch (error) {
    console.error('请求音频权限失败:', error);
    return false;
  }
};

/**
 * 显示音频权限提示
 */
export const showAudioPermissionPrompt = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    
    const dialog = document.createElement('div');
    dialog.style.cssText = `
      background: white;
      padding: 24px;
      border-radius: 8px;
      max-width: 400px;
      text-align: center;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    `;
    
    dialog.innerHTML = `
      <h3 style="margin: 0 0 16px 0; color: #333;">启用音频播放</h3>
      <p style="margin: 0 0 20px 0; color: #666; line-height: 1.5;">
        为了播放语音回复，需要您的授权。<br>
        点击"允许"按钮来启用音频播放功能。
      </p>
      <div style="display: flex; gap: 12px; justify-content: center;">
        <button id="allow-audio" style="
          background: #1890ff;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">允许</button>
        <button id="deny-audio" style="
          background: #f5f5f5;
          color: #666;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">取消</button>
      </div>
    `;
    
    modal.appendChild(dialog);
    document.body.appendChild(modal);
    
    const allowBtn = dialog.querySelector('#allow-audio') as HTMLButtonElement;
    const denyBtn = dialog.querySelector('#deny-audio') as HTMLButtonElement;
    
    const cleanup = () => {
      document.body.removeChild(modal);
    };
    
    allowBtn.onclick = async () => {
      cleanup();
      const granted = await requestAudioPermission();
      resolve(granted);
    };
    
    denyBtn.onclick = () => {
      cleanup();
      resolve(false);
    };
    
    // 点击背景关闭
    modal.onclick = (e) => {
      if (e.target === modal) {
        cleanup();
        resolve(false);
      }
    };
  });
};

/**
 * 自动处理音频权限
 * 如果需要权限，会自动显示提示
 */
export const ensureAudioPermission = async (): Promise<boolean> => {
  const status = await checkAudioPermission();
  
  if (status.hasPermission) {
    return true;
  }
  
  if (status.needsUserInteraction) {
    console.log('需要用户交互来启用音频播放');
    return await showAudioPermissionPrompt();
  }
  
  console.error('音频权限检查失败:', status.error);
  return false;
};
